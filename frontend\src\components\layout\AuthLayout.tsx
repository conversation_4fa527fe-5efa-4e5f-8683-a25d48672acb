import React from 'react'
import { SparklesIcon } from '@heroicons/react/24/outline'

interface AuthLayoutProps {
  children: React.ReactNode
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 left-20 w-32 h-32 bg-primary-400 rounded-full blur-2xl opacity-20 animate-float"></div>
          <div className="absolute top-40 right-40 w-48 h-48 bg-secondary-400 rounded-full blur-3xl opacity-20 animate-pulse-slow"></div>
          <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-primary-300 rounded-full blur-xl opacity-30 animate-bounce-slow"></div>
          <div className="absolute bottom-40 right-20 w-40 h-40 bg-secondary-300 rounded-full blur-2xl opacity-15 animate-float" style={{animationDelay: '2s'}}></div>
        </div>
      </div>

      <div className="relative z-10 flex flex-col justify-center py-8 sm:py-12 px-4 sm:px-6 lg:px-8 min-h-screen">
        {/* Logo Section */}
        <div className="sm:mx-auto sm:w-full sm:max-w-md mb-6 sm:mb-8">
          <div className="text-center animate-fade-in-up">
            <div className="flex justify-center mb-4 sm:mb-6">
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl sm:rounded-3xl flex items-center justify-center shadow-2xl animate-float">
                <SparklesIcon className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
              </div>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gradient mb-2">Character.AI</h1>
            <p className="text-sm sm:text-base lg:text-lg text-gray-600 dark:text-gray-400 px-4">
              Where AI comes to life through conversation
            </p>
          </div>
        </div>

        {/* Form Container */}
        <div className="mx-4 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="card-glass p-6 sm:p-8 lg:p-10 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
            {children}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-6 sm:mt-8 px-4 animate-fade-in-up" style={{animationDelay: '0.4s'}}>
          <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
            © 2024 Character.AI. Bringing AI personalities to life.
          </p>
        </div>
      </div>
    </div>
  )
}

export default AuthLayout
# Character.AI Clone

A full-stack application that recreates the Character.AI experience, allowing users to create, customize, and interact with AI-powered characters through dynamic conversations.

## Features

- 🤖 **AI Character Creation**: Build characters with unique personalities and traits
- 💬 **Real-time Chat**: Live messaging with WebSocket support
- 🔐 **Secure Authentication**: JWT-based auth with refresh token rotation
- 🎨 **Character Browser**: Discover and search community characters
- ⚡ **Fast & Responsive**: Built with modern technologies for optimal performance
- 🧪 **Comprehensive Testing**: Unit, integration, and E2E tests

## Tech Stack

### Backend
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Caching**: Redis
- **Real-time**: Socket.IO
- **AI Integration**: HelpingAI Dhanishtha-2.0 API
- **Authentication**: JWT with refresh tokens

### Frontend (Coming Soon)
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Redux Toolkit + RTK Query
- **Real-time**: Socket.IO Client
- **Routing**: React Router v6

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- PostgreSQL 15+
- Redis 7+
- HelpingAI API key

### Option 1: Docker Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd character-ai-clone
   ```

2. **Set up environment variables**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your configuration
   ```

3. **Start services with Docker**
   ```bash
   # Start PostgreSQL and Redis
   docker-compose up postgres redis -d
   
   # Run database migrations
   cd backend
   npm install
   npx prisma migrate dev
   npx prisma db seed
   
   # Start development server
   npm run dev
   ```

### Option 2: Local Setup

1. **Install PostgreSQL and Redis locally**
   
2. **Clone and install dependencies**
   ```bash
   git clone <repository-url>
   cd character-ai-clone/backend
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your local database URLs
   ```

4. **Initialize database**
   ```bash
   npx prisma migrate dev
   npx prisma db seed
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

## Environment Variables

### Backend (.env)

```bash
# Database
DATABASE_URL=postgresql://characterai:characterai_password@localhost:5432/characterai_dev

# Redis
REDIS_URL=redis://localhost:6379

# JWT Secrets (Change in production!)
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# HelpingAI
HELPINGAI_API_KEY=your-helpingai-api-key-here
HELPINGAI_MODEL=Dhanishtha-2.0-preview
HELPINGAI_MAX_TOKENS=2000

# Server
NODE_ENV=development
PORT=3001
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## API Documentation

The API is RESTful with the following main endpoints:

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user info

### Characters
- `GET /api/characters` - List/search characters
- `POST /api/characters` - Create new character
- `GET /api/characters/:id` - Get character details
- `PUT /api/characters/:id` - Update character
- `DELETE /api/characters/:id` - Delete character
- `POST /api/characters/:id/fork` - Fork character

### Conversations
- `GET /api/conversations` - Get user conversations
- `POST /api/conversations` - Start new conversation
- `GET /api/conversations/:id` - Get conversation details
- `GET /api/conversations/:id/messages` - Get messages
- `DELETE /api/conversations/:id` - Delete conversation

### WebSocket Events
- `join_conversation` - Join conversation room
- `send_message` - Send chat message
- `typing_start/stop` - Typing indicators
- `message_received` - Receive new messages

## Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm run start        # Start production server

# Database
npm run db:migrate   # Run Prisma migrations
npm run db:seed      # Seed database with test data
npm run db:studio    # Open Prisma Studio
npm run db:reset     # Reset database

# Testing
npm run test         # Run all tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run prettier     # Format code
```

### Project Structure

```
backend/
├── src/
│   ├── controllers/     # Request handlers
│   ├── services/        # Business logic
│   ├── middleware/      # Express middleware
│   ├── routes/          # API routes
│   ├── schemas/         # Zod validation schemas
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   ├── config/          # Configuration files
│   └── socket/          # Socket.IO handlers
├── prisma/              # Database schema and migrations
├── tests/               # Test files
└── logs/                # Application logs
```

## Testing

### Run Tests

```bash
# Unit tests
npm run test

# Integration tests
npm run test:integration

# E2E tests (when implemented)
npm run test:e2e

# Test coverage
npm run test:coverage
```

### Test Database

Tests use a separate test database. Set up:

```bash
# Start test database
docker-compose --profile test up postgres_test -d

# Run tests
npm run test
```

## Deployment

### Production Build

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Docker Production

```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write tests for new features
- Update documentation as needed

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in .env
   - Run `npm run db:migrate`

2. **Redis connection errors**
   - Ensure Redis is running
   - Check REDIS_URL in .env

3. **OpenAI API errors**
   - Verify OPENAI_API_KEY is set
   - Check API key has sufficient credits

4. **Port already in use**
   - Change PORT in .env
   - Kill process using port: `lsof -ti:3001 | xargs kill`

### Getting Help

- Check the [Issues](../../issues) page
- Review the [API Documentation](#api-documentation)
- Examine the test files for usage examples

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Roadmap

- [ ] Frontend React application
- [ ] Character avatar upload and generation
- [ ] Advanced AI personality customization
- [ ] Voice chat capabilities
- [ ] Mobile app (React Native)
- [ ] Character marketplace
- [ ] Premium features and subscriptions

---

**Note**: This is a development project for educational purposes. Please ensure compliance with HelpingAI's usage policies and terms of service.
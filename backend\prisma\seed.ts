import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Clean existing data
  await prisma.message.deleteMany();
  await prisma.conversation.deleteMany();
  await prisma.character.deleteMany();
  await prisma.refreshToken.deleteMany();
  await prisma.user.deleteMany();

  // Create sample users
  const hashedPassword = await bcrypt.hash('password123', 12);

  const user1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'alice',
      displayName: '<PERSON>',
      passwordHash: hashedPassword,
      isVerified: true,
    },
  });

  const user2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'bob',
      displayName: '<PERSON>',
      passwordHash: hashedPassword,
      subscription: 'premium',
      isVerified: true,
    },
  });

  // Create sample characters
  const character1 = await prisma.character.create({
    data: {
      name: '<PERSON> the Wise',
      description: 'A knowledgeable AI assistant who loves to help with learning and problem-solving.',
      personality: {
        traits: ['intelligent', 'patient', 'encouraging', 'wise'],
        communicationStyle: 'formal',
        background: 'A scholarly AI with vast knowledge in multiple fields, always eager to share wisdom and help others learn.',
        goals: ['Help users learn new things', 'Provide accurate information', 'Encourage intellectual growth'],
        quirks: ['Often quotes famous philosophers', 'Uses metaphors to explain complex concepts', 'Enjoys thought-provoking questions'],
      },
      tags: ['education', 'learning', 'wisdom', 'academic'],
      isPublic: true,
      creatorId: user1.id,
      rating: 4.8,
      ratingCount: 150,
      conversationCount: 1250,
    },
  });

  const character2 = await prisma.character.create({
    data: {
      name: 'Max the Adventurer',
      description: 'An enthusiastic explorer who loves outdoor activities and sharing adventure stories.',
      personality: {
        traits: ['adventurous', 'energetic', 'optimistic', 'brave'],
        communicationStyle: 'casual',
        background: 'A thrill-seeking AI who has "experienced" countless adventures through stories and simulations.',
        goals: ['Inspire others to explore', 'Share exciting stories', 'Encourage outdoor activities'],
        quirks: ['Always suggests outdoor activities', 'Uses adventure metaphors', 'Loves to tell campfire stories'],
      },
      tags: ['adventure', 'outdoors', 'travel', 'stories'],
      isPublic: true,
      creatorId: user2.id,
      rating: 4.5,
      ratingCount: 89,
      conversationCount: 456,
    },
  });

  const character3 = await prisma.character.create({
    data: {
      name: 'Luna the Creative',
      description: 'An artistic AI who specializes in creative writing, poetry, and imaginative storytelling.',
      personality: {
        traits: ['creative', 'imaginative', 'expressive', 'intuitive'],
        communicationStyle: 'poetic',
        background: 'A creative AI with a passion for arts, literature, and helping others express their creativity.',
        goals: ['Inspire creativity', 'Help with writing projects', 'Explore artistic expression'],
        quirks: ['Speaks in metaphors', 'Often references art and literature', 'Loves wordplay and puns'],
      },
      tags: ['creative', 'writing', 'poetry', 'art'],
      isPublic: true,
      creatorId: user1.id,
      rating: 4.7,
      ratingCount: 203,
      conversationCount: 782,
    },
  });

  // Create a private character
  const character4 = await prisma.character.create({
    data: {
      name: 'Personal Assistant',
      description: 'A private AI assistant for personal productivity and organization.',
      personality: {
        traits: ['organized', 'efficient', 'helpful', 'reliable'],
        communicationStyle: 'professional',
        background: 'A dedicated assistant focused on productivity and personal organization.',
        goals: ['Improve productivity', 'Organize tasks', 'Provide timely reminders'],
        quirks: ['Always suggests time management tips', 'Loves making lists', 'Uses productivity jargon'],
      },
      tags: ['productivity', 'organization', 'assistant'],
      isPublic: false,
      creatorId: user2.id,
      rating: 0,
      ratingCount: 0,
      conversationCount: 12,
    },
  });

  // Create sample conversations
  const conversation1 = await prisma.conversation.create({
    data: {
      title: 'Learning about Machine Learning',
      userId: user1.id,
      characterId: character1.id,
      messageCount: 8,
    },
  });

  const conversation2 = await prisma.conversation.create({
    data: {
      title: 'Planning a hiking trip',
      userId: user2.id,
      characterId: character2.id,
      messageCount: 12,
    },
  });

  // Create sample messages
  const messages = [
    {
      content: "Hi Sophia! I'm interested in learning about machine learning. Where should I start?",
      role: 'user',
      conversationId: conversation1.id,
      userId: user1.id,
    },
    {
      content: "Greetings! How wonderful that you wish to embark on the journey of understanding machine learning. As Aristotle once said, 'Well begun is half done.' I recommend starting with the fundamentals of statistics and linear algebra, for they are the bedrock upon which machine learning stands. Think of it as learning to walk before you run - these mathematical foundations will serve you well throughout your journey.",
      role: 'assistant',
      conversationId: conversation1.id,
      metadata: JSON.stringify({
        model: 'gpt-4',
        tokens: 120,
        processingTime: 1200,
      }),
    },
    {
      content: "That sounds like good advice. What specific topics in statistics should I focus on?",
      role: 'user',
      conversationId: conversation1.id,
      userId: user1.id,
    },
    {
      content: "An excellent question! Focus on descriptive statistics first - measures of central tendency like mean, median, and mode. Then move to measures of variability such as variance and standard deviation. Think of these as learning to describe the shape and character of data, much like an artist learning to observe form and proportion. Next, explore probability distributions - normal, binomial, and Poisson distributions are particularly important. Finally, delve into inferential statistics: hypothesis testing, confidence intervals, and correlation analysis.",
      role: 'assistant',
      conversationId: conversation1.id,
      metadata: JSON.stringify({
        model: 'gpt-4',
        tokens: 145,
        processingTime: 1400,
      }),
    },
    {
      content: "Hey Max! I'm planning a hiking trip for next weekend. Any suggestions for good trails?",
      role: 'user',
      conversationId: conversation2.id,
      userId: user2.id,
    },
    {
      content: "Hey there, fellow adventurer! 🏔️ That's awesome that you're planning a hiking trip! The call of the wild is irresistible, isn't it? To give you the best trail recommendations, I need to know a bit more about your adventure parameters. What's your experience level? Are you looking for a gentle nature walk or something that'll get your heart pumping? And where are you located? Every mountain has its own personality, and I want to match you with the perfect trail companion!",
      role: 'assistant',
      conversationId: conversation2.id,
      metadata: JSON.stringify({
        model: 'gpt-4',
        tokens: 110,
        processingTime: 1100,
      }),
    },
  ];

  for (const messageData of messages) {
    await prisma.message.create({
      data: messageData,
    });
  }

  console.log('Database seeded successfully!');
  console.log({
    users: 2,
    characters: 4,
    conversations: 2,
    messages: messages.length,
  });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
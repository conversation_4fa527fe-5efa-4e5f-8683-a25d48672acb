import { PrismaClient } from '@prisma/client';
import { RedisService } from '@/config/redis';
import { JWTService } from '@/utils/jwt';
import { PasswordService } from '@/utils/password';
import { logger } from '@/utils/logger';
import { AppError } from '@/middleware/errorHandler';
import {
  RegisterInput,
  LoginInput,
  RefreshTokenInput,
  AuthResponse,
  User,
  UpdateUserInput,
  ChangePasswordInput,
} from '@/types/auth';

export class AuthService {
  constructor(
    private prisma: PrismaClient,
    private redisService: RedisService
  ) {}

  // Register a new user
  async register(data: RegisterInput): Promise<AuthResponse> {
    try {
      const { email, username, displayName, password } = data;

      // Check if user already exists
      const existingUser = await this.prisma.user.findFirst({
        where: {
          OR: [{ email }, { username }],
        },
      });

      if (existingUser) {
        if (existingUser.email === email) {
          throw new AppError('User with this email already exists', 400);
        }
        if (existingUser.username === username) {
          throw new AppError('Username is already taken', 400);
        }
      }

      // Validate password strength
      const passwordValidation = PasswordService.validatePasswordStrength(password);
      if (!passwordValidation.isValid) {
        throw new AppError('Password does not meet requirements', 400, passwordValidation.errors);
      }

      // Hash password
      const passwordHash = await PasswordService.hashPassword(password);

      // Create user
      const user = await this.prisma.user.create({
        data: {
          email,
          username,
          displayName,
          passwordHash,
        },
        select: {
          id: true,
          email: true,
          username: true,
          displayName: true,
          avatarUrl: true,
          isVerified: true,
          subscription: true,
          createdAt: true,
        },
      });

      // Generate tokens
      const { accessToken, refreshToken } = await this.generateTokens(user.id);

      logger.info(`New user registered: ${user.username} (${user.email})`);

      return {
        user,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Registration error:', error);
      throw new AppError('Registration failed', 500);
    }
  }

  // Login user
  async login(data: LoginInput): Promise<AuthResponse> {
    try {
      const { email, password } = data;

      // Find user by email
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new AppError('Invalid email or password', 401);
      }

      // Verify password
      const isPasswordValid = await PasswordService.comparePassword(password, user.passwordHash);
      if (!isPasswordValid) {
        throw new AppError('Invalid email or password', 401);
      }

      // Generate tokens
      const { accessToken, refreshToken } = await this.generateTokens(user.id);

      logger.info(`User logged in: ${user.username} (${user.email})`);

      return {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          displayName: user.displayName,
          avatarUrl: user.avatarUrl,
          isVerified: user.isVerified,
          subscription: user.subscription,
          createdAt: user.createdAt,
        },
        accessToken,
        refreshToken,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Login error:', error);
      throw new AppError('Login failed', 500);
    }
  }

  // Refresh access token
  async refreshToken(data: RefreshTokenInput): Promise<AuthResponse> {
    try {
      const { refreshToken } = data;

      // Verify refresh token
      const payload = JWTService.verifyRefreshToken(refreshToken);

      // Check if refresh token exists in database
      const storedToken = await this.prisma.refreshToken.findUnique({
        where: { id: payload.tokenId },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              username: true,
              displayName: true,
              avatarUrl: true,
              isVerified: true,
              subscription: true,
              createdAt: true,
            },
          },
        },
      });

      if (!storedToken || storedToken.token !== refreshToken) {
        throw new AppError('Invalid refresh token', 401);
      }

      // Check if token is expired
      if (storedToken.expiresAt < new Date()) {
        // Delete expired token
        await this.prisma.refreshToken.delete({
          where: { id: storedToken.id },
        });
        throw new AppError('Refresh token expired', 401);
      }

      // Generate new tokens (token rotation)
      const { accessToken: newAccessToken, refreshToken: newRefreshToken } = 
        await this.generateTokens(storedToken.userId);

      // Delete old refresh token
      await this.prisma.refreshToken.delete({
        where: { id: storedToken.id },
      });

      return {
        user: storedToken.user,
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Token refresh error:', error);
      throw new AppError('Token refresh failed', 500);
    }
  }

  // Logout user (invalidate refresh token)
  async logout(userId: string, refreshToken?: string): Promise<void> {
    try {
      if (refreshToken) {
        // Delete specific refresh token
        await this.prisma.refreshToken.deleteMany({
          where: {
            userId,
            token: refreshToken,
          },
        });
      } else {
        // Delete all refresh tokens for user (logout from all devices)
        await this.prisma.refreshToken.deleteMany({
          where: { userId },
        });
      }

      // Also blacklist the access token in Redis
      // (This would require storing all active tokens, which is optional)
      
      logger.info(`User logged out: ${userId}`);
    } catch (error) {
      logger.error('Logout error:', error);
      throw new AppError('Logout failed', 500);
    }
  }

  // Get current user
  async getCurrentUser(userId: string): Promise<User> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          username: true,
          displayName: true,
          avatarUrl: true,
          isVerified: true,
          subscription: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new AppError('User not found', 404);
      }

      return user;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Get current user error:', error);
      throw new AppError('Failed to get user', 500);
    }
  }

  // Update user profile
  async updateProfile(userId: string, data: UpdateUserInput): Promise<User> {
    try {
      const user = await this.prisma.user.update({
        where: { id: userId },
        data,
        select: {
          id: true,
          email: true,
          username: true,
          displayName: true,
          avatarUrl: true,
          isVerified: true,
          subscription: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info(`User profile updated: ${user.username}`);
      return user;
    } catch (error) {
      logger.error('Update profile error:', error);
      throw new AppError('Failed to update profile', 500);
    }
  }

  // Change password
  async changePassword(userId: string, data: ChangePasswordInput): Promise<void> {
    try {
      const { currentPassword, newPassword } = data;

      // Get user with password
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new AppError('User not found', 404);
      }

      // Verify current password
      const isCurrentPasswordValid = await PasswordService.comparePassword(
        currentPassword,
        user.passwordHash
      );

      if (!isCurrentPasswordValid) {
        throw new AppError('Current password is incorrect', 400);
      }

      // Validate new password strength
      const passwordValidation = PasswordService.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        throw new AppError('New password does not meet requirements', 400, passwordValidation.errors);
      }

      // Hash new password
      const newPasswordHash = await PasswordService.hashPassword(newPassword);

      // Update password
      await this.prisma.user.update({
        where: { id: userId },
        data: { passwordHash: newPasswordHash },
      });

      // Invalidate all refresh tokens (force re-login on all devices)
      await this.prisma.refreshToken.deleteMany({
        where: { userId },
      });

      logger.info(`Password changed for user: ${user.username}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Change password error:', error);
      throw new AppError('Failed to change password', 500);
    }
  }

  // Generate access and refresh tokens
  private async generateTokens(userId: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Get user info for token payload
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          username: true,
        },
      });

      if (!user) {
        throw new AppError('User not found', 404);
      }

      // Generate access token
      const accessToken = JWTService.generateAccessToken({
        userId: user.id,
        email: user.email,
        username: user.username,
      });

      // Create refresh token record
      const refreshTokenRecord = await this.prisma.refreshToken.create({
        data: {
          userId,
          token: '', // Will be updated below
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      // Generate refresh token with token ID
      const refreshToken = JWTService.generateRefreshToken({
        userId,
        tokenId: refreshTokenRecord.id,
      });

      // Update the token in the database
      await this.prisma.refreshToken.update({
        where: { id: refreshTokenRecord.id },
        data: { token: refreshToken },
      });

      return { accessToken, refreshToken };
    } catch (error) {
      logger.error('Token generation error:', error);
      throw new AppError('Failed to generate tokens', 500);
    }
  }

  // Clean up expired refresh tokens (should be run periodically)
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await this.prisma.refreshToken.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      logger.info(`Cleaned up ${result.count} expired refresh tokens`);
    } catch (error) {
      logger.error('Token cleanup error:', error);
    }
  }
}

export default AuthService;
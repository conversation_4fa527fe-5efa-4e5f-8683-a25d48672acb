export interface ClientToServerEvents {
  join_conversation: (conversationId: string) => void;
  leave_conversation: (conversationId: string) => void;
  send_message: (data: SendMessageData) => void;
  typing_start: (conversationId: string) => void;
  typing_stop: (conversationId: string) => void;
}

export interface ServerToClientEvents {
  message_received: (message: SocketMessage) => void;
  typing_start: (data: TypingData) => void;
  typing_stop: (data: TypingData) => void;
  user_joined: (data: UserJoinedData) => void;
  user_left: (data: UserLeftData) => void;
  conversation_updated: (conversation: SocketConversation) => void;
  error: (error: SocketErrorData) => void;
}

export interface SocketData {
  userId: string;
  username: string;
  displayName: string;
  isAuthenticated: boolean;
}

export interface SendMessageData {
  conversationId: string;
  content: string;
}

export interface SocketMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  createdAt: Date;
  conversationId: string;
  user?: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
  };
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
  };
}

export interface TypingData {
  conversationId: string;
  userId: string;
  username: string;
  isTyping: boolean;
}

export interface UserJoinedData {
  conversationId: string;
  userId: string;
  username: string;
  displayName: string;
}

export interface UserLeftData {
  conversationId: string;
  userId: string;
  username: string;
}

export interface SocketConversation {
  id: string;
  title?: string;
  lastMessageAt: Date;
  messageCount: number;
  character: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
}

export interface SocketErrorData {
  message: string;
  code?: string;
  conversationId?: string;
}

export interface AuthenticatedSocket {
  id: string;
  userId: string;
  username: string;
  displayName: string;
  join: (room: string) => void;
  leave: (room: string) => void;
  emit: (event: string, data: any) => void;
  to: (room: string) => any;
  broadcast: any;
  data: SocketData;
}
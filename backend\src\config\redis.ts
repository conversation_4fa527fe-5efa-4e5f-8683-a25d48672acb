import { RedisClientType } from 'redis';
import { logger } from '@/utils/logger';

// Simplified Redis configuration for local testing
// This version completely skips Redis when not available

export async function connectRedis(): Promise<RedisClientType | null> {
  // For local testing, we skip Redis entirely
  logger.info('Redis disabled for local testing');
  return null;
}

export function getRedisClient(): RedisClientType | null {
  return null;
}

export async function disconnectRedis(): Promise<void> {
  logger.info('Redis disconnect - nothing to disconnect (local testing mode)');
}

// Redis utility functions - mock implementation for local testing
export class RedisService {
  async set(key: string, value: string, expiration?: number): Promise<void> {
    logger.debug(`Redis mock: SET ${key} (local testing - operation skipped)`);
  }

  async get(key: string): Promise<string | null> {
    logger.debug(`Redis mock: GET ${key} (local testing - returning null)`);
    return null;
  }

  async del(key: string): Promise<number> {
    logger.debug(`Redis mock: DEL ${key} (local testing - returning 0)`);
    return 0;
  }

  async exists(key: string): Promise<number> {
    logger.debug(`Redis mock: EXISTS ${key} (local testing - returning 0)`);
    return 0;
  }

  async setJSON(key: string, value: object, expiration?: number): Promise<void> {
    logger.debug(`Redis mock: SET JSON ${key} (local testing - operation skipped)`);
  }

  async getJSON<T>(key: string): Promise<T | null> {
    logger.debug(`Redis mock: GET JSON ${key} (local testing - returning null)`);
    return null;
  }

  async setAdd(key: string, member: string): Promise<number> {
    logger.debug(`Redis mock: SADD ${key} ${member} (local testing - returning 0)`);
    return 0;
  }

  async setRemove(key: string, member: string): Promise<number> {
    logger.debug(`Redis mock: SREM ${key} ${member} (local testing - returning 0)`);
    return 0;
  }

  async getMembers(key: string): Promise<string[]> {
    logger.debug(`Redis mock: SMEMBERS ${key} (local testing - returning empty array)`);
    return [];
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    logger.debug(`Redis mock: EXPIRE ${key} ${seconds} (local testing - returning false)`);
    return false;
  }
}

// Export a singleton instance
export const redisService = new RedisService();

export default { connectRedis, getRedisClient, disconnectRedis, RedisService, redisService };
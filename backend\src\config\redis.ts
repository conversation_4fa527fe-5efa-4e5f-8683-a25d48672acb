import { createClient, RedisClientType } from 'redis';
import { logger } from '@/utils/logger';
import { getConfig } from './services';

let redisClient: RedisClientType | null = null;

export async function connectRedis(): Promise<RedisClientType | null> {
  const config = getConfig();

  if (!config.redis.enabled) {
    logger.info('Redis disabled - running in mock mode');
    return null;
  }

  try {
    redisClient = createClient({
      url: config.redis.url,
      socket: {
        connectTimeout: config.redis.connectTimeout,
        lazyConnect: config.redis.lazyConnect,
        reconnectStrategy: (retries) => {
          if (retries > config.redis.maxRetriesPerRequest) {
            logger.error('Redis max retries exceeded');
            return false;
          }
          return Math.min(retries * config.redis.retryDelayOnFailover, 3000);
        }
      }
    });

    redisClient.on('error', (err) => {
      logger.error('Redis client error:', err);
    });

    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      logger.info('Redis client ready');
    });

    redisClient.on('end', () => {
      logger.info('Redis client disconnected');
    });

    await redisClient.connect();
    logger.info('Successfully connected to Redis');
    return redisClient;
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    redisClient = null;
    throw error;
  }
}

export function getRedisClient(): RedisClientType | null {
  return redisClient;
}

export async function disconnectRedis(): Promise<void> {
  if (redisClient) {
    try {
      await redisClient.quit();
      redisClient = null;
      logger.info('Redis disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from Redis:', error);
      throw error;
    }
  } else {
    logger.info('Redis disconnect - no active connection');
  }
}

// Redis utility functions - supports both real and mock operations
export class RedisService {
  private get client(): RedisClientType | null {
    return getRedisClient();
  }

  private get isMockMode(): boolean {
    return !getConfig().redis.enabled || !this.client;
  }

  async set(key: string, value: string, expiration?: number): Promise<void> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: SET ${key} (mock mode - operation skipped)`);
      return;
    }

    try {
      if (expiration) {
        await this.client!.setEx(key, expiration, value);
      } else {
        await this.client!.set(key, value);
      }
      logger.debug(`Redis: SET ${key} (${expiration ? `expires in ${expiration}s` : 'no expiration'})`);
    } catch (error) {
      logger.error(`Redis SET error for key ${key}:`, error);
      throw error;
    }
  }

  async get(key: string): Promise<string | null> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: GET ${key} (mock mode - returning null)`);
      return null;
    }

    try {
      const value = await this.client!.get(key);
      logger.debug(`Redis: GET ${key} (${value ? 'found' : 'not found'})`);
      return value;
    } catch (error) {
      logger.error(`Redis GET error for key ${key}:`, error);
      throw error;
    }
  }

  async del(key: string): Promise<number> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: DEL ${key} (mock mode - returning 0)`);
      return 0;
    }

    try {
      const result = await this.client!.del(key);
      logger.debug(`Redis: DEL ${key} (deleted ${result} keys)`);
      return result;
    } catch (error) {
      logger.error(`Redis DEL error for key ${key}:`, error);
      throw error;
    }
  }

  async exists(key: string): Promise<number> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: EXISTS ${key} (mock mode - returning 0)`);
      return 0;
    }

    try {
      const result = await this.client!.exists(key);
      logger.debug(`Redis: EXISTS ${key} (${result ? 'exists' : 'does not exist'})`);
      return result;
    } catch (error) {
      logger.error(`Redis EXISTS error for key ${key}:`, error);
      throw error;
    }
  }

  async setJSON(key: string, value: object, expiration?: number): Promise<void> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: SET JSON ${key} (mock mode - operation skipped)`);
      return;
    }

    try {
      const jsonValue = JSON.stringify(value);
      if (expiration) {
        await this.client!.setEx(key, expiration, jsonValue);
      } else {
        await this.client!.set(key, jsonValue);
      }
      logger.debug(`Redis: SET JSON ${key} (${expiration ? `expires in ${expiration}s` : 'no expiration'})`);
    } catch (error) {
      logger.error(`Redis SET JSON error for key ${key}:`, error);
      throw error;
    }
  }

  async getJSON<T>(key: string): Promise<T | null> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: GET JSON ${key} (mock mode - returning null)`);
      return null;
    }

    try {
      const value = await this.client!.get(key);
      if (!value) {
        logger.debug(`Redis: GET JSON ${key} (not found)`);
        return null;
      }
      const parsed = JSON.parse(value) as T;
      logger.debug(`Redis: GET JSON ${key} (found)`);
      return parsed;
    } catch (error) {
      logger.error(`Redis GET JSON error for key ${key}:`, error);
      throw error;
    }
  }

  async setAdd(key: string, member: string): Promise<number> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: SADD ${key} ${member} (mock mode - returning 0)`);
      return 0;
    }

    try {
      const result = await this.client!.sAdd(key, member);
      logger.debug(`Redis: SADD ${key} ${member} (added ${result} members)`);
      return result;
    } catch (error) {
      logger.error(`Redis SADD error for key ${key}:`, error);
      throw error;
    }
  }

  async setRemove(key: string, member: string): Promise<number> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: SREM ${key} ${member} (mock mode - returning 0)`);
      return 0;
    }

    try {
      const result = await this.client!.sRem(key, member);
      logger.debug(`Redis: SREM ${key} ${member} (removed ${result} members)`);
      return result;
    } catch (error) {
      logger.error(`Redis SREM error for key ${key}:`, error);
      throw error;
    }
  }

  async getMembers(key: string): Promise<string[]> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: SMEMBERS ${key} (mock mode - returning empty array)`);
      return [];
    }

    try {
      const members = await this.client!.sMembers(key);
      logger.debug(`Redis: SMEMBERS ${key} (found ${members.length} members)`);
      return members;
    } catch (error) {
      logger.error(`Redis SMEMBERS error for key ${key}:`, error);
      throw error;
    }
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    if (this.isMockMode) {
      logger.debug(`Redis mock: EXPIRE ${key} ${seconds} (mock mode - returning false)`);
      return false;
    }

    try {
      const result = await this.client!.expire(key, seconds);
      logger.debug(`Redis: EXPIRE ${key} ${seconds} (${result ? 'success' : 'failed'})`);
      return result;
    } catch (error) {
      logger.error(`Redis EXPIRE error for key ${key}:`, error);
      throw error;
    }
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    if (this.isMockMode) {
      logger.debug('Redis health check: mock mode - returning true');
      return true;
    }

    try {
      const result = await this.client!.ping();
      const isHealthy = result === 'PONG';
      logger.debug(`Redis health check: ${isHealthy ? 'healthy' : 'unhealthy'}`);
      return isHealthy;
    } catch (error) {
      logger.error('Redis health check error:', error);
      return false;
    }
  }
}

// Export a singleton instance
export const redisService = new RedisService();

export default { connectRedis, getRedisClient, disconnectRedis, RedisService, redisService };
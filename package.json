{"name": "character-ai-clone", "version": "1.0.0", "description": "Full-stack Character.AI clone application", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio", "db:reset": "cd backend && npm run db:reset", "db:generate": "cd backend && npm run db:generate", "kill:node": "taskkill /f /im node.exe", "clean:ports": "npm run kill:node", "setup": "npm run install:all && npm run db:generate && npm run db:migrate"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["ai", "chatbot", "character", "fullstack", "react", "express", "typescript"], "author": "Character AI <PERSON>", "license": "MIT", "workspaces": ["backend", "frontend"], "dependencies": {"helpingai": "^1.0.1", "ts-node": "^10.9.2"}}
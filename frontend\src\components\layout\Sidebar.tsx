import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { clsx } from 'clsx'
import {
  HomeIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  UserGroupIcon,
  SparklesIcon,
  BookmarkIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'
import {
  HomeIcon as HomeIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  PlusIcon as PlusIconSolid,
  UserGroupIcon as UserGroupIconSolid,
  BookmarkIcon as BookmarkIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid
} from '@heroicons/react/24/solid'
import { RootState } from '@/store'
import { setSidebarTab, setSidebarOpen } from '@/store/slices/uiSlice'

const navigation = [
  { 
    name: 'Home', 
    href: '/', 
    icon: HomeIcon, 
    iconSolid: HomeIconSolid, 
    tab: 'home',
    description: 'Dashboard & overview'
  },
  { 
    name: 'Browse', 
    href: '/browse', 
    icon: MagnifyingGlassIcon, 
    iconSolid: MagnifyingGlassIconSolid, 
    tab: 'browse',
    description: 'Discover characters'
  },
  { 
    name: 'Create', 
    href: '/create', 
    icon: PlusIcon, 
    iconSolid: PlusIconSolid, 
    tab: 'create',
    description: 'Build new character'
  },
  { 
    name: 'My Characters', 
    href: '/my-characters', 
    icon: UserGroupIcon, 
    iconSolid: UserGroupIconSolid, 
    tab: 'characters',
    description: 'Your creations'
  },
]

const secondaryNavigation = [
  {
    name: 'Favorites',
    href: '/favorites',
    icon: BookmarkIcon,
    iconSolid: BookmarkIconSolid,
    tab: 'favorites'
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Cog6ToothIcon,
    iconSolid: Cog6ToothIconSolid,
    tab: 'settings'
  }
]

const Sidebar: React.FC = () => {
  const location = useLocation()
  const dispatch = useDispatch()
  const { isOpen, activeTab } = useSelector((state: RootState) => state.ui.sidebar)

  const handleTabChange = (tab: string) => {
    dispatch(setSidebarTab(tab))
    // Close sidebar on mobile after navigation
    if (window.innerWidth < 1024) {
      dispatch(setSidebarOpen(false))
    }
  }

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden animate-fade-in"
          onClick={() => dispatch(setSidebarOpen(false))}
          onTouchStart={(e) => {
            e.preventDefault()
            dispatch(setSidebarOpen(false))
          }}
        />
      )}
      
      {/* Sidebar */}
      <div
        className={clsx(
          'fixed lg:relative inset-y-0 left-0 z-50 lg:z-auto flex-shrink-0 transition-all duration-300 ease-in-out',
          isOpen ? 'w-64 translate-x-0' : 'w-16 -translate-x-full lg:translate-x-0',
          'glass border-r border-white/20 dark:border-gray-700/20 backdrop-blur-xl'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center h-14 sm:h-16 px-3 sm:px-4 border-b border-white/20 dark:border-gray-700/20">
            {isOpen ? (
              <div className="flex items-center animate-fade-in-up">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <SparklesIcon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                  </div>
                </div>
                <div className="ml-2 sm:ml-3 min-w-0">
                  <p className="text-base sm:text-lg font-bold text-gradient truncate">Character.AI</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 hidden sm:block">Create & Chat</p>
                </div>
              </div>
            ) : (
              <div className="flex justify-center w-full">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <SparklesIcon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                </div>
              </div>
            )}
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 sm:py-6 space-y-1 sm:space-y-2 overflow-y-auto">
            {/* Primary Navigation */}
            <div className="space-y-1">
              {navigation.map((item, index) => {
                const isActive = location.pathname === item.href
                const Icon = isActive ? item.iconSolid : item.icon

                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => handleTabChange(item.tab)}
                    className={clsx(
                      'group flex items-center px-2 sm:px-3 py-2.5 sm:py-3 text-sm font-semibold rounded-xl sm:rounded-2xl transition-all duration-200 relative',
                      isActive
                        ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg transform scale-105'
                        : 'text-gray-700 dark:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-gray-100'
                    )}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <Icon
                      className={clsx(
                        'flex-shrink-0 h-5 w-5 sm:h-6 sm:w-6 transition-all duration-200',
                        isActive ? 'text-white' : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
                      )}
                    />
                    {isOpen && (
                      <div className="ml-2 sm:ml-3 animate-fade-in-up min-w-0">
                        <span className="block truncate">{item.name}</span>
                        {!isActive && (
                          <span className="text-xs opacity-70 hidden sm:block">{item.description}</span>
                        )}
                      </div>
                    )}
                    {isActive && isOpen && (
                      <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-1 h-6 sm:h-8 bg-white rounded-full shadow-md" />
                    )}
                  </Link>
                )
              })}
            </div>

            {/* Divider */}
            {isOpen && (
              <div className="py-3 sm:py-4">
                <div className="h-px bg-gray-200 dark:bg-gray-700" />
              </div>
            )}

            {/* Secondary Navigation */}
            <div className="space-y-1">
              {isOpen && (
                <p className="px-2 sm:px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2 sm:mb-3">
                  More
                </p>
              )}
              {secondaryNavigation.map((item, index) => {
                const isActive = location.pathname === item.href
                const Icon = isActive ? item.iconSolid : item.icon

                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => handleTabChange(item.tab)}
                    className={clsx(
                      'group flex items-center px-2 sm:px-3 py-2 text-sm font-medium rounded-lg sm:rounded-xl transition-all duration-200',
                      isActive
                        ? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-gray-100'
                    )}
                  >
                    <Icon
                      className={clsx(
                        'flex-shrink-0 h-4 w-4 sm:h-5 sm:w-5',
                        isActive ? 'text-gray-700 dark:text-gray-300' : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-400'
                      )}
                    />
                    {isOpen && <span className="ml-2 sm:ml-3 truncate">{item.name}</span>}
                  </Link>
                )
              })}
            </div>
          </nav>

          {/* Quick Create Button */}
          {isOpen && (
            <div className="px-2 pb-4 sm:pb-6">
              <Link
                to="/create"
                className="w-full btn-primary flex items-center justify-center py-2.5 sm:py-3 rounded-xl sm:rounded-2xl group hover:scale-105 transition-all duration-300 text-sm sm:text-base"
              >
                <PlusIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                <span className="font-semibold">New Character</span>
              </Link>
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default Sidebar
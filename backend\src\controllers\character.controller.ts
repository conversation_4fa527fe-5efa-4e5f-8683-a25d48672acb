import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { CharacterService } from '@/services/character.service';
import { prisma } from '@/config/database';
import {
  CreateCharacterInput,
  UpdateCharacterInput,
  CharacterFiltersInput,
} from '@/schemas/character.schemas';

// Initialize character service
const characterService = new CharacterService(prisma);

// @desc    Create a new character
// @route   POST /api/characters
// @access  Private
export const createCharacter = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const { avatarUrl, ...rest } = req.body;
  
  // Clean the data to match the expected types
  const characterData = {
    ...rest,
    avatarUrl: avatarUrl === null ? undefined : avatarUrl,
  } as CreateCharacterInput;

  logger.info('Character creation attempt:', { name: characterData.name, userId });

  const character = await characterService.createCharacter(characterData, userId);

  res.status(201).json({
    success: true,
    message: 'Character created successfully',
    data: { character },
  });
});

// @desc    Get characters with search and filtering
// @route   GET /api/characters
// @access  Public
export const getCharacters = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user?.id;
  const filters: CharacterFiltersInput = req.query as any;

  logger.info('Characters fetch request:', { filters, userId });

  const result = await characterService.searchCharacters(filters, userId);

  res.json({
    success: true,
    message: 'Characters fetched successfully',
    data: result,
  });
});

// @desc    Get character by ID
// @route   GET /api/characters/:id
// @access  Public
export const getCharacter = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user?.id;

  logger.info('Character fetch by ID:', { id, userId });

  const character = await characterService.getCharacter(id, userId);

  res.json({
    success: true,
    data: { character },
  });
});

// @desc    Update character (owner only)
// @route   PUT /api/characters/:id
// @access  Private
export const updateCharacter = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const { avatarUrl, ...rest } = req.body;
  
  // Clean the data to match the expected types
  const updateData = {
    ...rest,
    avatarUrl: avatarUrl === null ? undefined : avatarUrl,
  } as UpdateCharacterInput;

  logger.info('Character update attempt:', { id, userId });

  const character = await characterService.updateCharacter(id, updateData, userId);

  res.json({
    success: true,
    message: 'Character updated successfully',
    data: { character },
  });
});

// @desc    Delete character (owner only)
// @route   DELETE /api/characters/:id
// @access  Private
export const deleteCharacter = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  logger.info('Character deletion attempt:', { id, userId });

  await characterService.deleteCharacter(id, userId);

  res.json({
    success: true,
    message: 'Character deleted successfully',
  });
});

export default {
  createCharacter,
  getCharacters,
  getCharacter,
  updateCharacter,
  deleteCharacter,
};
# Environment Configuration Template
# Copy this file to .env and fill in the actual values

# Application
NODE_ENV=production
PORT=3001

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/characterai
POSTGRES_DB=characterai
POSTGRES_USER=postgres
POSTGRES_PASSWORD=secure_password

# Redis
REDIS_URL=redis://localhost:6379

# JWT Secrets (Generate secure random strings)
JWT_SECRET=your_super_secure_jwt_secret_here
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_here

# HelpingAI API
HELPINGAI_API_KEY=your_helpingai_api_key_here
HELPINGAI_MODEL=Dhanishtha-2.0-preview
HELPINGAI_MAX_TOKENS=2000

# CORS
CORS_ORIGIN=http://localhost:3000

# Frontend URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session
SESSION_SECRET=your_session_secret_here

# Email (if implementing email features)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Monitoring (if using APM tools)
# NEW_RELIC_LICENSE_KEY=your_new_relic_key
# SENTRY_DSN=your_sentry_dsn

# Docker Compose Override
COMPOSE_PROJECT_NAME=characterai
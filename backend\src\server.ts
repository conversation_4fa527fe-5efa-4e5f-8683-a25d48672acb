import dotenv from 'dotenv';
import { createApp } from './app';
import { logger } from '@/utils/logger';
import { connectDatabase } from '@/config/database';
import { connectRedis } from '@/config/redis';
import { validateEnvironment, getConfig } from '@/config/services';

// Load environment variables
dotenv.config();

// Validate environment variables early
try {
  validateEnvironment();
  logger.info('Environment validation passed');
} catch (error) {
  logger.error('Environment validation failed:', error);
  process.exit(1);
}

const PORT = process.env.PORT || 3001;

async function startServer() {
  try {
    // Load service configuration
    const config = getConfig();
    logger.info(`Starting server in ${config.environment} mode`);

    // Connect to database
    await connectDatabase();
    logger.info('Database connected successfully');

    // Connect to Redis (conditionally)
    if (config.redis.enabled) {
      await connectRedis();
      logger.info('Redis connected successfully');
    } else {
      logger.info('Redis disabled - running in mock mode');
    }

    // Create Express app with Socket.IO
    const { server } = createApp();

    // Start server
    server.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`Health check: http://localhost:${PORT}/health`);
      logger.info(`Auth endpoint: http://localhost:${PORT}/api/auth/register`);
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully');
      server.close(() => {
        logger.info('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully');
      server.close(() => {
        logger.info('Server closed');
        process.exit(0);
      });
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

startServer();
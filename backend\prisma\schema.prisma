// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  displayName String
  avatarUrl   String?
  isVerified  <PERSON>ole<PERSON>  @default(false)
  subscription String  @default("free") // "free" | "premium"
  passwordHash String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  characters    Character[]
  conversations Conversation[]
  messages      Message[]
  refreshTokens RefreshToken[]

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model Character {
  id               String   @id @default(cuid())
  name             String
  description      String
  personality      String   // JSON string for PersonalityTraits object
  avatarUrl        String?
  isPublic         Boolean  @default(true)
  tags             String   // Comma-separated tags
  conversationCount Int     @default(0)
  rating           Float    @default(0)
  ratingCount      Int      @default(0)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  creatorId     String
  creator       User      @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  forkedFromId  String?
  forkedFrom    Character? @relation("CharacterFork", fields: [forkedFromId], references: [id])
  forks         Character[] @relation("CharacterFork")
  conversations Conversation[]

  // Indexes for performance (simplified for SQLite)
  @@index([isPublic])
  @@index([creatorId])
  @@map("characters")
}

model Conversation {
  id            String   @id @default(cuid())
  title         String?
  lastMessageAt DateTime @default(now())
  messageCount  Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  characterId String
  character   Character @relation(fields: [characterId], references: [id], onDelete: Cascade)
  messages    Message[]

  // Indexes for performance (simplified for SQLite)
  @@index([userId])
  @@index([characterId])
  @@map("conversations")
}

model Message {
  id        String   @id @default(cuid())
  content   String
  role      String   // "user" | "assistant"
  metadata  String?  // JSON string for processing info, tokens, etc.
  createdAt DateTime @default(now())

  // Relations
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  userId         String?
  user           User?        @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Indexes for performance
  @@index([conversationId, createdAt])
  @@map("messages")
}
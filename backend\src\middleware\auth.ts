import { Request, Response, NextFunction } from 'express';
import { JWTService } from '@/utils/jwt';
import { AppError } from './errorHandler';
import { prisma } from '@/config/database';
import { JWTPayload } from '@/types/auth';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        username: string;
        displayName: string;
        avatarUrl?: string;
        isVerified: boolean;
        subscription: string;
      };
    }
  }
}

// Extract token from request headers
const extractToken = (req: Request): string | null => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check for token in cookies (for browser clients)
  if (req.headers.cookie) {
    const match = req.headers.cookie.match(/accessToken=([^;]+)/);
    if (match) {
      return match[1];
    }
  }
  
  return null;
};

// Middleware to authenticate JWT token
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractToken(req);

    if (!token) {
      throw new AppError('Access token is required', 401);
    }

    // Verify the token
    const payload: JWTPayload = JWTService.verifyAccessToken(token);

    // Fetch user from database to ensure they still exist and get latest data
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        avatarUrl: true,
        isVerified: true,
        subscription: true,
      },
    });

    if (!user) {
      throw new AppError('User not found', 401);
    }

    // Attach user to request object
    req.user = {
      ...user,
      avatarUrl: user.avatarUrl || undefined,
    };
    next();
  } catch (error) {
    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('Invalid or expired token', 401));
    }
  }
};

// Optional authentication middleware (doesn't throw error if no token)
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractToken(req);

    if (token) {
      // Verify the token
      const payload: JWTPayload = JWTService.verifyAccessToken(token);

      // Fetch user from database
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          username: true,
          displayName: true,
          avatarUrl: true,
          isVerified: true,
          subscription: true,
        },
      });

      if (user) {
        req.user = {
          ...user,
          avatarUrl: user.avatarUrl || undefined,
        };
      }
    }

    next();
  } catch (error) {
    // Don't throw error for optional auth, just continue without user
    next();
  }
};

// Middleware to check if user is verified
export const requireVerification = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return next(new AppError('Authentication required', 401));
  }

  if (!req.user.isVerified) {
    return next(new AppError('Email verification required', 403));
  }

  next();
};

// Middleware to check premium subscription
export const requirePremium = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return next(new AppError('Authentication required', 401));
  }

  if (req.user.subscription !== 'premium') {
    return next(new AppError('Premium subscription required', 403));
  }

  next();
};

// Middleware to check if user owns a resource
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AppError('Authentication required', 401));
    }

    const resourceId = req.params[resourceIdParam];
    if (!resourceId) {
      return next(new AppError('Resource ID required', 400));
    }

    // This is a generic ownership check - specific implementations
    // should override this or use more specific middleware
    req.resourceId = resourceId;
    next();
  };
};

// Socket.IO authentication middleware
export const authenticateSocket = async (socket: any, next: any) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return next(new Error('Authentication token required'));
    }

    // Verify the token
    const payload: JWTPayload = JWTService.verifyAccessToken(token);

    // Fetch user from database
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        avatarUrl: true,
        isVerified: true,
        subscription: true,
      },
    });

    if (!user) {
      return next(new Error('User not found'));
    }

    // Attach user data to socket
    socket.userId = user.id;
    socket.user = user;
    next();
  } catch (error) {
    next(new Error('Invalid or expired token'));
  }
};

declare global {
  namespace Express {
    interface Request {
      resourceId?: string;
    }
  }
}

export default authenticateToken;
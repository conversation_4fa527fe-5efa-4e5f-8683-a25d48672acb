import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function migrateRoles() {
  try {
    logger.info('Starting role migration from "character" to "assistant"...');

    // Update all messages with role 'character' to 'assistant'
    const result = await prisma.message.updateMany({
      where: {
        role: 'character'
      },
      data: {
        role: 'assistant'
      }
    });

    logger.info(`Successfully updated ${result.count} messages from role "character" to "assistant"`);

    // Verify the migration
    const remainingCharacterMessages = await prisma.message.count({
      where: {
        role: 'character'
      }
    });

    const assistantMessages = await prisma.message.count({
      where: {
        role: 'assistant'
      }
    });

    logger.info(`Migration verification:`);
    logger.info(`- Remaining "character" role messages: ${remainingCharacterMessages}`);
    logger.info(`- Total "assistant" role messages: ${assistantMessages}`);

    if (remainingCharacterMessages === 0) {
      logger.info('✅ Migration completed successfully! All character roles have been converted to assistant.');
    } else {
      logger.warn(`⚠️ Migration incomplete. ${remainingCharacterMessages} messages still have "character" role.`);
    }

  } catch (error) {
    logger.error('Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  migrateRoles()
    .then(() => {
      logger.info('Migration script completed.');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration script failed:', error);
      process.exit(1);
    });
}

export default migrateRoles;
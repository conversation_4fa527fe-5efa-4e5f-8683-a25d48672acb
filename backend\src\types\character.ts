export interface PersonalityTraits {
  traits: string[];
  communicationStyle: 'casual' | 'formal' | 'playful' | 'professional' | 'poetic';
  background: string;
  goals: string[];
  quirks: string[];
  [key: string]: any; // Add index signature for Prisma compatibility
}

export interface CreateCharacterInput {
  name: string;
  description: string;
  personality: PersonalityTraits;
  avatarUrl?: string | null;
  isPublic?: boolean;
  tags: string[];
}

export interface UpdateCharacterInput {
  name?: string;
  description?: string;
  personality?: PersonalityTraits;
  avatarUrl?: string | null;
  isPublic?: boolean;
  tags?: string[];
}

export interface CharacterFilters {
  search?: string;
  tags?: string[];
  creatorId?: string;
  isPublic?: boolean;
  sortBy?: 'newest' | 'oldest' | 'rating' | 'popular';
  page?: number;
  limit?: number;
}

export interface CharacterSearchResult {
  characters: Character[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface Character {
  id: string;
  name: string;
  description: string;
  personality: PersonalityTraits;
  avatarUrl: string | null;
  isPublic: boolean;
  tags: string[];
  conversationCount: number;
  rating: number;
  ratingCount: number;
  createdAt: Date;
  updatedAt: Date;
  creatorId: string;
  forkedFromId?: string | null;
  creator?: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl: string | null;
  };
}
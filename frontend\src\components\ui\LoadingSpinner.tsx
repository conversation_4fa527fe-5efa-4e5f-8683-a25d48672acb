import React from 'react'
import { clsx } from 'clsx'

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  text?: string
  variant?: 'primary' | 'secondary' | 'white' | 'gray'
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className,
  text,
  variant = 'primary'
}) => {
  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12',
  }

  const variantClasses = {
    primary: 'border-primary-200 dark:border-primary-800 border-t-primary-600 dark:border-t-primary-400',
    secondary: 'border-secondary-200 dark:border-secondary-800 border-t-secondary-600 dark:border-t-secondary-400',
    white: 'border-white/30 border-t-white',
    gray: 'border-gray-200 dark:border-gray-700 border-t-gray-600 dark:border-t-gray-400',
  }

  return (
    <div className={clsx('flex flex-col items-center justify-center gap-3', className)}>
      <div className="relative">
        <div
          className={clsx(
            'animate-spin rounded-full border-2',
            sizeClasses[size],
            variantClasses[variant]
          )}
        />
        {/* Glow effect for larger spinners */}
        {(size === 'lg' || size === 'xl') && (
          <div
            className={clsx(
              'absolute inset-0 rounded-full opacity-30 blur-sm',
              variant === 'primary' && 'bg-gradient-to-r from-primary-500 to-primary-600',
              variant === 'secondary' && 'bg-gradient-to-r from-secondary-500 to-secondary-600',
              variant === 'white' && 'bg-white',
              variant === 'gray' && 'bg-gray-500'
            )}
          />
        )}
      </div>
      {text && (
        <p className="text-sm font-medium text-gray-600 dark:text-gray-400 animate-pulse">
          {text}
        </p>
      )}
    </div>
  )
}

export default LoadingSpinner
import { Server as SocketIOServer, Socket } from 'socket.io';
import { authenticateSocket } from '@/middleware/auth';
import { logger } from '@/utils/logger';
import { ChatService } from '@/services/chat.service';
import { prisma } from '@/config/database';
import { redisService } from '@/config/redis';
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
  SendMessageData,
} from '@/types/socket';

// Initialize chat service
const chatService = new ChatService(prisma, redisService);

// Extended socket interface with user data
interface AuthenticatedSocket extends Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData> {
  userId: string;
  user: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
    isVerified: boolean;
    subscription: string;
  };
}

export function socketHandler(io: SocketIOServer<ClientToServerEvents, ServerToClientEvents, any, SocketData>) {
  // Authentication middleware for Socket.IO
  io.use(authenticateSocket);

  io.on('connection', (socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>) => {
    const authSocket = socket as any as AuthenticatedSocket;
    logger.info(`User ${authSocket.user.username} connected with socket ID: ${socket.id}`);

    // Join user to their personal room for notifications
    socket.join(`user:${authSocket.userId}`);

    // Handle joining a conversation
    socket.on('join_conversation', async (conversationId: string) => {
      try {
        // Verify user has access to this conversation
        const conversation = await prisma.conversation.findUnique({
          where: { id: conversationId },
          include: {
            character: {
              select: {
                id: true,
                name: true,
                isPublic: true,
                creatorId: true,
              },
            },
          },
        });

        if (!conversation) {
          socket.emit('error', {
            message: 'Conversation not found',
            conversationId,
          });
          return;
        }

        // Check access permissions
        const hasAccess = 
          conversation.userId === authSocket.userId || // User owns conversation
          conversation.character.isPublic || // Character is public
          conversation.character.creatorId === authSocket.userId; // User owns character

        if (!hasAccess) {
          socket.emit('error', {
            message: 'Access denied to this conversation',
            conversationId,
          });
          return;
        }

        socket.join(`conversation:${conversationId}`);
        logger.info(`User ${authSocket.user.username} joined conversation ${conversationId}`);
        
        // Notify others in the conversation
        socket.to(`conversation:${conversationId}`).emit('user_joined', {
          conversationId,
          userId: authSocket.userId,
          username: authSocket.user.username,
          displayName: authSocket.user.displayName,
        });
      } catch (error) {
        logger.error('Error joining conversation:', error);
        socket.emit('error', {
          message: 'Failed to join conversation',
          conversationId,
        });
      }
    });

    // Handle leaving a conversation
    socket.on('leave_conversation', async (conversationId: string) => {
      try {
        socket.leave(`conversation:${conversationId}`);
        logger.info(`User ${authSocket.user.username} left conversation ${conversationId}`);
        
        // Notify others in the conversation
        socket.to(`conversation:${conversationId}`).emit('user_left', {
          conversationId,
          userId: authSocket.userId,
          username: authSocket.user.username,
        });
      } catch (error) {
        logger.error('Error leaving conversation:', error);
        socket.emit('error', {
          message: 'Failed to leave conversation',
          conversationId,
        });
      }
    });

    // Handle sending a message
    socket.on('send_message', async (data: SendMessageData) => {
      try {
        const { conversationId, content } = data;
        
        // Validate input
        if (!conversationId || !content || content.trim().length === 0) {
          socket.emit('error', {
            message: 'Invalid message data',
            conversationId,
          });
          return;
        }

        if (content.length > 2000) {
          socket.emit('error', {
            message: 'Message too long (max 2000 characters)',
            conversationId,
          });
          return;
        }

        // Show typing indicator for AI processing
        socket.to(`conversation:${conversationId}`).emit('typing_start', {
          conversationId,
          userId: 'ai',
          username: 'AI',
          isTyping: true,
        });

        // Process message and generate AI response
        const { userMessage, aiResponse } = await chatService.processMessage(
          conversationId,
          content.trim(),
          authSocket.userId
        );

        // Stop typing indicator
        socket.to(`conversation:${conversationId}`).emit('typing_stop', {
          conversationId,
          userId: 'ai',
          username: 'AI',
          isTyping: false,
        });

        // Emit user message to all participants
        io.to(`conversation:${conversationId}`).emit('message_received', {
          id: userMessage.id,
          content: userMessage.content,
          role: userMessage.role,
          createdAt: userMessage.createdAt,
          conversationId,
          user: userMessage.user,
        });

        // Emit AI response to all participants
        io.to(`conversation:${conversationId}`).emit('message_received', {
          id: aiResponse.id,
          content: aiResponse.content,
          role: aiResponse.role,
          createdAt: aiResponse.createdAt,
          conversationId,
          metadata: aiResponse.metadata,
        });

        logger.info(`Message processed in conversation ${conversationId}: user ${authSocket.user.username}`);
      } catch (error) {
        logger.error('Error sending message:', error);
        
        // Stop typing indicator on error
        socket.to(`conversation:${data.conversationId}`).emit('typing_stop', {
          conversationId: data.conversationId,
          userId: authSocket.userId,
          username: authSocket.user.username,
          isTyping: false,
        });

        socket.emit('error', {
          message: error instanceof Error ? error.message : 'Failed to send message',
          conversationId: data.conversationId,
        });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', async (conversationId: string) => {
      try {
        await chatService.setTypingStatus(conversationId, authSocket.userId, true);
        socket.to(`conversation:${conversationId}`).emit('typing_start', {
          conversationId,
          userId: authSocket.userId,
          username: authSocket.user.username,
          isTyping: true,
        });
      } catch (error) {
        logger.error('Error handling typing start:', error);
      }
    });

    socket.on('typing_stop', async (conversationId: string) => {
      try {
        await chatService.setTypingStatus(conversationId, authSocket.userId, false);
        socket.to(`conversation:${conversationId}`).emit('typing_stop', {
          conversationId,
          userId: authSocket.userId,
          username: authSocket.user.username,
          isTyping: false,
        });
      } catch (error) {
        logger.error('Error handling typing stop:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason: string) => {
      logger.info(`User ${authSocket.user.username} disconnected: ${reason}`);
      
      // Leave all conversation rooms
      const rooms = Array.from(socket.rooms);
      rooms.forEach((room: string) => {
        if (room.startsWith('conversation:')) {
          const conversationId = room.replace('conversation:', '');
          socket.to(room).emit('user_left', {
            conversationId,
            userId: authSocket.userId,
            username: authSocket.user.username,
          });
        }
      });
    });

    // Handle connection errors (remove this as connect_error is not a client event)
    // socket.on('connect_error', (error: any) => {
    //   logger.error(`Socket connection error for user ${authSocket.user.username}:`, error);
    // });
  });

  // Handle connection errors at the server level
  io.on('connect_error', (error: any) => {
    logger.error('Socket.IO connection error:', error);
  });

  logger.info('Socket.IO server initialized');
}

export default socketHandler;
import React from 'react'
import { ChatBubbleLeftRightIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline'

const ChatPage: React.FC = () => {
  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="text-center max-w-md mx-auto">
          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl sm:rounded-3xl flex items-center justify-center mx-auto mb-4 sm:mb-6 animate-float">
            <ChatBubbleLeftRightIcon className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
          </div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">Chat Interface</h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-6 sm:mb-8">
            This is where the chat interface will be implemented. Start conversations with your favorite AI characters.
          </p>
          <button className="btn-primary px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base active:scale-95 transition-all duration-200">
            <PaperAirplaneIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
            Start Chatting
          </button>
        </div>
      </div>
    </div>
  )
}

export default ChatPage
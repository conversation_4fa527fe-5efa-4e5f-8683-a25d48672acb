/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }
  
  html {
    @apply scroll-smooth;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    @apply bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-900 dark:text-gray-100 antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Improve touch targets for mobile */
  button, a, [role="button"] {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve scrolling on iOS */
  .scroll-smooth {
    -webkit-overflow-scrolling: touch;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center rounded-xl px-4 py-2.5 text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transform hover:scale-[1.02] active:scale-[0.98];
  }
  
  .btn-primary {
    @apply btn bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 focus:ring-primary-500 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply btn bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 focus:ring-secondary-500 shadow-lg hover:shadow-xl;
  }
  
  .btn-ghost {
    @apply btn bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-200 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 focus:ring-gray-500 backdrop-blur-sm;
  }

  .btn-outline {
    @apply btn border-2 border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:ring-primary-500;
  }

  .btn-danger {
    @apply btn bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl;
  }
  
  /* Input Components */
  .input {
    @apply block w-full rounded-xl border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm transition-all duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 sm:text-sm placeholder:text-gray-400 dark:placeholder:text-gray-500;
  }

  .input-error {
    @apply border-red-300 dark:border-red-600 focus:border-red-500 focus:ring-red-500/20;
  }
  
  /* Card Components */
  .card {
    @apply bg-white/80 dark:bg-gray-800/80 rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6 backdrop-blur-sm hover:shadow-xl transition-all duration-300;
  }

  .card-hover {
    @apply card hover:scale-[1.02] hover:-translate-y-1 cursor-pointer;
  }

  .card-glass {
    @apply bg-white/20 dark:bg-gray-800/20 backdrop-blur-md border border-white/30 dark:border-gray-700/30 rounded-2xl shadow-xl;
  }
  
  /* Avatar Components */
  .avatar {
    @apply inline-flex items-center justify-center rounded-full bg-gradient-to-br from-primary-500 to-secondary-500 text-white font-semibold ring-4 ring-white dark:ring-gray-800 shadow-lg;
  }
  
  .avatar-sm {
    @apply avatar h-8 w-8 text-xs;
  }
  
  .avatar-md {
    @apply avatar h-10 w-10 text-sm;
  }
  
  .avatar-lg {
    @apply avatar h-12 w-12 text-base;
  }
  
  .avatar-xl {
    @apply avatar h-16 w-16 text-lg;
  }

  .avatar-2xl {
    @apply avatar h-20 w-20 text-xl;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200;
  }

  .badge-secondary {
    @apply badge bg-secondary-100 dark:bg-secondary-900/30 text-secondary-800 dark:text-secondary-200;
  }

  .badge-success {
    @apply badge bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200;
  }

  .badge-warning {
    @apply badge bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200;
  }

  .badge-error {
    @apply badge bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200;
  }

  /* Gradient Text */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .text-gradient-lg {
    @apply bg-gradient-to-r from-primary-500 via-secondary-500 to-primary-600 bg-clip-text text-transparent;
  }

  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
  }

  /* Glassmorphism */
  .glass {
    @apply bg-white/20 dark:bg-gray-800/20 backdrop-blur-md border border-white/30 dark:border-gray-700/30;
  }

  .glass-strong {
    @apply bg-white/40 dark:bg-gray-800/40 backdrop-blur-lg border border-white/50 dark:border-gray-700/50;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .text-balance {
    text-wrap: balance;
  }

  /* Touch-friendly utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .tap-highlight-none {
    -webkit-tap-highlight-color: transparent;
  }

  /* Mobile-first responsive text */
  .text-responsive {
    font-size: clamp(0.875rem, 4vw, 1rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.125rem, 5vw, 1.5rem);
  }

  .text-responsive-xl {
    font-size: clamp(1.5rem, 6vw, 2.25rem);
  }

  /* Perspective utilities for 3D effects */
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  /* Custom gradient backgrounds */
  .bg-gradient-radial {
    background: radial-gradient(ellipse at center, var(--tw-gradient-stops));
  }

  .bg-gradient-conic {
    background: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
  }
}

/* Custom animations */
@keyframes typing {
  0%, 60% {
    opacity: 1;
  }
  30% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
  }
}

.typing-indicator {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator:nth-child(3) {
  animation-delay: 0.4s;
}

.shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
  background-size: 800px 104px;
}

.float {
  animation: float 3s ease-in-out infinite;
}

.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Dark mode transitions */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}
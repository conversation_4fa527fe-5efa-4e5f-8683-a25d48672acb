// Shared types between frontend and backend

export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  isVerified: boolean;
  subscription: string;
  createdAt: string;
  updatedAt?: string;
}

export interface PersonalityTraits {
  traits: string[];
  communicationStyle: 'casual' | 'formal' | 'playful' | 'professional' | 'poetic';
  background: string;
  goals: string[];
  quirks: string[];
}

export interface Character {
  id: string;
  name: string;
  description: string;
  personality: PersonalityTraits;
  avatarUrl?: string;
  isPublic: boolean;
  tags: string[];
  conversationCount: number;
  rating: number;
  ratingCount: number;
  createdAt: string;
  updatedAt: string;
  creatorId: string;
  forkedFromId?: string;
  creator?: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
  };
  forkedFrom?: {
    id: string;
    name: string;
    creator: {
      username: string;
      displayName: string;
    };
  };
}

export interface Conversation {
  id: string;
  title?: string;
  lastMessageAt: string;
  messageCount: number;
  createdAt: string;
  updatedAt: string;
  userId: string;
  characterId: string;
  character?: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  user?: {
    id: string;
    username: string;
    displayName: string;
  };
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    regenerated?: boolean;
    regeneratedAt?: string;
    [key: string]: any;
  };
  createdAt: string;
  conversationId: string;
  userId?: string;
  user?: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
  };
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: {
    message: string;
    details?: any;
  };
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface CharacterSearchResult {
  characters: Character[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ConversationSearchResult {
  conversations: Conversation[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface MessageSearchResult {
  messages: Message[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Form input types
export interface LoginInput {
  email: string;
  password: string;
}

export interface RegisterInput {
  email: string;
  username: string;
  displayName: string;
  password: string;
  confirmPassword: string;
}

export interface CreateCharacterInput {
  name: string;
  description: string;
  personality: PersonalityTraits;
  avatarUrl?: string;
  isPublic?: boolean;
  tags: string[];
}

export interface UpdateCharacterInput {
  name?: string;
  description?: string;
  personality?: PersonalityTraits;
  avatarUrl?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface CreateConversationInput {
  characterId: string;
  title?: string;
}

// Socket.IO types
export interface SocketMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  createdAt: string;
  conversationId: string;
  user?: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
  };
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
  };
}

export interface TypingData {
  conversationId: string;
  userId: string;
  username: string;
  isTyping: boolean;
}

export interface UserJoinedData {
  conversationId: string;
  userId: string;
  username: string;
  displayName: string;
}

export interface UserLeftData {
  conversationId: string;
  userId: string;
  username: string;
}

export interface SocketErrorData {
  message: string;
  code?: string;
  conversationId?: string;
}

// Filter and search types
export interface CharacterFilters {
  search?: string;
  tags?: string[];
  creatorId?: string;
  isPublic?: boolean;
  sortBy?: 'newest' | 'oldest' | 'rating' | 'popular';
  page?: number;
  limit?: number;
}

export interface ConversationFilters {
  characterId?: string;
  search?: string;
  sortBy?: 'newest' | 'oldest' | 'mostActive';
  page?: number;
  limit?: number;
}

export interface MessageFilters {
  conversationId: string;
  role?: 'user' | 'assistant';
  beforeDate?: string;
  afterDate?: string;
  page?: number;
  limit?: number;
}
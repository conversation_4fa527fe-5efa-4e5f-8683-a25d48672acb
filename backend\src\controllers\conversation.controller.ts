import { Request, Response } from 'express';
import { ConversationService } from '@/services/conversation.service';
import { ChatService } from '@/services/chat.service';
import { prisma } from '@/config/database';
import { redisService } from '@/config/redis';
import { asyncHandler, AppError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

// Initialize services
const conversationService = new ConversationService(prisma);
const chatService = new ChatService(prisma, redisService);

// @desc    Get user's conversations
// @route   GET /api/conversations
// @access  Private
export const getConversations = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const filters = {
    characterId: req.query.characterId as string,
    search: req.query.search as string,
    sortBy: req.query.sortBy as 'newest' | 'oldest' | 'mostActive' || 'newest',
    page: parseInt(req.query.page as string) || 1,
    limit: parseInt(req.query.limit as string) || 20,
  };

  const result = await conversationService.getUserConversations(userId, filters);

  res.json({
    success: true,
    data: result,
  });
});

// @desc    Start a new conversation with a character
// @route   POST /api/conversations
// @access  Private
export const createConversation = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const { characterId, title } = req.body;

  const conversation = await conversationService.createConversation(
    { characterId, title },
    userId
  );

  res.status(201).json({
    success: true,
    message: 'Conversation started successfully',
    data: { conversation },
  });
});

// @desc    Get conversation details
// @route   GET /api/conversations/:id
// @access  Private
export const getConversation = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const conversation = await conversationService.getConversation(id, userId);

  res.json({
    success: true,
    data: { conversation },
  });
});

// @desc    Delete a conversation
// @route   DELETE /api/conversations/:id
// @access  Private
export const deleteConversation = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  await conversationService.deleteConversation(id, userId);

  res.json({
    success: true,
    message: 'Conversation deleted successfully',
  });
});

// @desc    Get messages for a conversation
// @route   GET /api/conversations/:id/messages
// @access  Private
export const getMessages = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const filters = {
    conversationId: id,
    role: req.query.role as 'user' | 'assistant',
    beforeDate: req.query.beforeDate ? new Date(req.query.beforeDate as string) : undefined,
    afterDate: req.query.afterDate ? new Date(req.query.afterDate as string) : undefined,
    page: parseInt(req.query.page as string) || 1,
    limit: parseInt(req.query.limit as string) || 50,
  };

  const result = await conversationService.getMessages(id, userId, filters);

  res.json({
    success: true,
    data: result,
  });
});

// @desc    Send a message in a conversation (REST fallback)
// @route   POST /api/conversations/:id/messages
// @access  Private
export const sendMessage = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const { content } = req.body;

  // Process message and generate AI response
  const { userMessage, aiResponse } = await chatService.processMessage(id, content, userId);

  res.status(201).json({
    success: true,
    message: 'Message sent successfully',
    data: {
      userMessage,
      aiResponse,
    },
  });
});

// @desc    Update conversation title
// @route   PUT /api/conversations/:id/title
// @access  Private
export const updateConversationTitle = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const { title } = req.body;

  const conversation = await conversationService.updateConversationTitle(id, title, userId);

  res.json({
    success: true,
    message: 'Conversation title updated successfully',
    data: { conversation },
  });
});

// @desc    Get conversation statistics
// @route   GET /api/conversations/:id/stats
// @access  Private
export const getConversationStats = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const stats = await conversationService.getConversationStats(id, userId);

  res.json({
    success: true,
    data: { stats },
  });
});

// @desc    Regenerate AI response
// @route   POST /api/conversations/messages/:messageId/regenerate
// @access  Private
export const regenerateResponse = asyncHandler(async (req: Request, res: Response) => {
  const { messageId } = req.params;
  const userId = req.user!.id;

  const updatedMessage = await chatService.regenerateResponse(messageId, userId);

  res.json({
    success: true,
    message: 'Response regenerated successfully',
    data: { message: updatedMessage },
  });
});

// @desc    Delete a message
// @route   DELETE /api/conversations/messages/:messageId
// @access  Private
export const deleteMessage = asyncHandler(async (req: Request, res: Response) => {
  const { messageId } = req.params;
  const userId = req.user!.id;

  await chatService.deleteMessage(messageId, userId);

  res.json({
    success: true,
    message: 'Message deleted successfully',
  });
});

// @desc    Get AI service health status
// @route   GET /api/conversations/ai/health
// @access  Public
export const getAIHealth = asyncHandler(async (req: Request, res: Response) => {
  const health = await chatService.getAIHealthStatus();

  res.json({
    success: true,
    data: { health },
  });
});

export default {
  getConversations,
  createConversation,
  getConversation,
  deleteConversation,
  getMessages,
  sendMessage,
  updateConversationTitle,
  getConversationStats,
  regenerateResponse,
  deleteMessage,
  getAIHealth,
};
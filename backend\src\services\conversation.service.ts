import { PrismaClient, Prisma } from '@prisma/client';
import { logger } from '@/utils/logger';
import { AppError } from '@/middleware/errorHandler';
import {
  CreateConversationInput,
  SendMessageInput,
  Conversation,
  Message,
  ConversationFilters,
  MessageFilters,
  ConversationSearchResult,
  MessageSearchResult,
  PaginationInput,
} from '@/types/conversation';

export class ConversationService {
  constructor(private prisma: PrismaClient) {}

  // Start a new conversation with a character
  async createConversation(data: CreateConversationInput, userId: string): Promise<Conversation> {
    try {
      const { characterId, title } = data;

      // Verify character exists and is accessible
      const character = await this.prisma.character.findUnique({
        where: { id: characterId },
      });

      if (!character) {
        throw new AppError('Character not found', 404);
      }

      // Check if character is public or user owns it
      if (!character.isPublic && character.creatorId !== userId) {
        throw new AppError('Character not accessible', 403);
      }

      // Create conversation
      const conversation = await this.prisma.conversation.create({
        data: {
          userId,
          characterId,
          title: title || `Chat with ${character.name}`,
        },
        include: {
          character: {
            select: {
              id: true,
              name: true,
              avatarUrl: true,
            },
          },
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
            },
          },
        },
      });

      logger.info(`Conversation created: ${conversation.id} between user ${userId} and character ${characterId}`);
      return {
        ...conversation,
        title: conversation.title ?? undefined,
        character: conversation.character ? {
          ...conversation.character,
          avatarUrl: conversation.character.avatarUrl ?? undefined,
        } : undefined,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Create conversation error:', error);
      throw new AppError('Failed to create conversation', 500);
    }
  }

  // Get conversation by ID
  async getConversation(id: string, userId: string): Promise<Conversation> {
    try {
      const conversation = await this.prisma.conversation.findUnique({
        where: { id },
        include: {
          character: {
            select: {
              id: true,
              name: true,
              avatarUrl: true,
            },
          },
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
            },
          },
        },
      });

      if (!conversation) {
        throw new AppError('Conversation not found', 404);
      }

      // Check if user owns this conversation
      if (conversation.userId !== userId) {
        throw new AppError('Not authorized to access this conversation', 403);
      }

      return {
        ...conversation,
        title: conversation.title ?? undefined,
        character: conversation.character ? {
          ...conversation.character,
          avatarUrl: conversation.character.avatarUrl ?? undefined,
        } : undefined,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Get conversation error:', error);
      throw new AppError('Failed to get conversation', 500);
    }
  }

  // Get user's conversations
  async getUserConversations(userId: string, filters: ConversationFilters): Promise<ConversationSearchResult> {
    try {
      const {
        characterId,
        search,
        sortBy = 'newest',
        page = 1,
        limit = 20,
      } = filters;

      const offset = (page - 1) * limit;

      // Build where clause
      const where: any = {
        userId,
        ...(characterId && { characterId }),
        ...(search && {
          OR: [
            { title: { contains: search, mode: 'insensitive' } },
            { character: { name: { contains: search, mode: 'insensitive' } } },
          ],
        }),
      };

      // Build order by clause
      let orderBy: any = {};
      switch (sortBy) {
        case 'oldest':
          orderBy = { createdAt: 'asc' };
          break;
        case 'mostActive':
          orderBy = { messageCount: 'desc' };
          break;
        case 'newest':
        default:
          orderBy = { lastMessageAt: 'desc' };
          break;
      }

      // Execute queries
      const [conversations, total] = await Promise.all([
        this.prisma.conversation.findMany({
          where,
          orderBy,
          skip: offset,
          take: limit,
          include: {
            character: {
              select: {
                id: true,
                name: true,
                avatarUrl: true,
              },
            },
          },
        }),
        this.prisma.conversation.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        conversations: conversations.map(conv => ({
          ...conv,
          title: conv.title ?? undefined,
          character: conv.character ? {
            ...conv.character,
            avatarUrl: conv.character.avatarUrl ?? undefined,
          } : undefined,
        })),
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error('Get user conversations error:', error);
      throw new AppError('Failed to get conversations', 500);
    }
  }

  // Delete conversation
  async deleteConversation(id: string, userId: string): Promise<void> {
    try {
      // Check if conversation exists and user owns it
      const conversation = await this.getConversation(id, userId);

      // Delete conversation (messages will be cascade deleted)
      await this.prisma.conversation.delete({
        where: { id },
      });

      logger.info(`Conversation deleted: ${id} by user ${userId}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Delete conversation error:', error);
      throw new AppError('Failed to delete conversation', 500);
    }
  }

  // Create a message (for REST API fallback)
  async createMessage(conversationId: string, content: string, userId: string): Promise<Message> {
    try {
      // Verify conversation exists and user owns it
      const conversation = await this.getConversation(conversationId, userId);

      // Create user message
      const message = await this.prisma.message.create({
        data: {
          conversationId,
          content,
          role: 'user',
          userId,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      });

      // Update conversation
      await this.prisma.conversation.update({
        where: { id: conversationId },
        data: {
          lastMessageAt: new Date(),
          messageCount: { increment: 1 },
        },
      });

      logger.info(`Message created: ${message.id} in conversation ${conversationId}`);
      return {
        ...message,
        role: message.role as 'user' | 'assistant',
        userId: message.userId ?? undefined,
        metadata: message.metadata ? (message.metadata as any) : undefined,
        user: message.user ? {
          ...message.user,
          avatarUrl: message.user.avatarUrl ?? undefined,
        } : undefined,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Create message error:', error);
      throw new AppError('Failed to create message', 500);
    }
  }

  // Get messages for a conversation
  async getMessages(conversationId: string, userId: string, filters: MessageFilters): Promise<MessageSearchResult> {
    try {
      // Verify conversation access
      await this.getConversation(conversationId, userId);

      const {
        role,
        beforeDate,
        afterDate,
        page = 1,
        limit = 50,
      } = filters;

      const offset = (page - 1) * limit;

      // Build where clause
      const where: any = {
        conversationId,
        ...(role && { role }),
        ...(beforeDate && { createdAt: { lt: new Date(beforeDate) } }),
        ...(afterDate && { createdAt: { gt: new Date(afterDate) } }),
      };

      // Execute queries
      const [messages, total] = await Promise.all([
        this.prisma.message.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatarUrl: true,
              },
            },
          },
        }),
        this.prisma.message.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        messages: messages.reverse().map(msg => ({
          ...msg,
          role: msg.role as 'user' | 'assistant',
          userId: msg.userId ?? undefined,
          metadata: msg.metadata ? (msg.metadata as any) : undefined,
          user: msg.user ? {
            ...msg.user,
            avatarUrl: msg.user.avatarUrl ?? undefined,
          } : undefined,
        })), // Reverse to show oldest first
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Get messages error:', error);
      throw new AppError('Failed to get messages', 500);
    }
  }

  // Update conversation title
  async updateConversationTitle(id: string, title: string, userId: string): Promise<Conversation> {
    try {
      // Verify conversation access
      await this.getConversation(id, userId);

      const conversation = await this.prisma.conversation.update({
        where: { id },
        data: { title },
        include: {
          character: {
            select: {
              id: true,
              name: true,
              avatarUrl: true,
            },
          },
        },
      });

      logger.info(`Conversation title updated: ${id} by user ${userId}`);
      return {
        ...conversation,
        title: conversation.title ?? undefined,
        character: conversation.character ? {
          ...conversation.character,
          avatarUrl: conversation.character.avatarUrl ?? undefined,
        } : undefined,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Update conversation title error:', error);
      throw new AppError('Failed to update conversation title', 500);
    }
  }

  // Get conversation statistics
  async getConversationStats(id: string, userId: string): Promise<{
    messageCount: number;
    userMessages: number;
    assistantMessages: number;
    averageResponseTime: number;
  }> {
    try {
      // Verify conversation access
      await this.getConversation(id, userId);

      const [userMessages, assistantMessages] = await Promise.all([
        this.prisma.message.count({
          where: { conversationId: id, role: 'user' },
        }),
        this.prisma.message.count({
          where: { conversationId: id, role: 'assistant' },
        }),
      ]);

      // Calculate average response time from message metadata
      // For now, we'll estimate based on typical response patterns
      const messagesWithMetadata = await this.prisma.message.findMany({
        where: { 
          conversationId: id, 
          role: 'assistant',
          metadata: { not: null }
        },
        select: { metadata: true },
        take: 10 // Sample recent messages
      });
      
      let averageResponseTime = 2500; // Default estimate in milliseconds
      if (messagesWithMetadata.length > 0) {
        const responseTimes = messagesWithMetadata
          .map(msg => (msg.metadata as any)?.responseTime)
          .filter(time => typeof time === 'number');
        
        if (responseTimes.length > 0) {
          averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
        }
      }

      return {
        messageCount: userMessages + assistantMessages,
        userMessages,
        assistantMessages,
        averageResponseTime,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Get conversation stats error:', error);
      throw new AppError('Failed to get conversation statistics', 500);
    }
  }

  // Clean up old conversations (utility method)
  async cleanupOldConversations(daysOld: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await this.prisma.conversation.deleteMany({
        where: {
          lastMessageAt: {
            lt: cutoffDate,
          },
          messageCount: 0, // Only delete conversations with no messages
        },
      });

      logger.info(`Cleaned up ${result.count} old conversations`);
      return result.count;
    } catch (error) {
      logger.error('Cleanup conversations error:', error);
      throw new AppError('Failed to cleanup conversations', 500);
    }
  }
}

export default ConversationService;
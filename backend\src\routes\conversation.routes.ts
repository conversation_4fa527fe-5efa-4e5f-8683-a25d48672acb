import { Router } from 'express';
import { authenticateToken, optionalAuth } from '@/middleware/auth';
import { chatRateLimiter } from '@/middleware/rateLimiter';
import { validateBody, validateParams, validateQuery, parseNumericQuery } from '@/middleware/validation';
import { z } from 'zod';
import conversationController from '@/controllers/conversation.controller';

const router = Router();

// Conversation schemas
const createConversationSchema = z.object({
  characterId: z.string().cuid('Invalid character ID'),
  title: z.string().max(100, 'Title must be less than 100 characters').optional(),
});

const conversationIdSchema = z.object({
  id: z.string().cuid('Invalid conversation ID'),
});

const sendMessageSchema = z.object({
  content: z.string().min(1, 'Message content is required').max(2000, 'Message must be less than 2000 characters'),
});

const conversationFiltersSchema = z.object({
  characterId: z.string().cuid().optional(),
  search: z.string().max(100).optional(),
  sortBy: z.enum(['newest', 'oldest', 'mostActive']).optional().default('newest'),
  page: z.number().int().min(1).optional().default(1),
  limit: z.number().int().min(1).max(50).optional().default(20),
});

const messageFiltersSchema = z.object({
  page: z.number().int().min(1).optional().default(1),
  limit: z.number().int().min(1).max(100).optional().default(50),
  role: z.enum(['user', 'assistant']).optional(),
  beforeDate: z.string().datetime().optional(),
  afterDate: z.string().datetime().optional(),
});

// @route   GET /api/conversations/ai/health
// @desc    Get AI service health status
// @access  Public
router.get('/ai/health',
  conversationController.getAIHealth
);

// @route   POST /api/conversations/messages/:messageId/regenerate
// @desc    Regenerate AI response
// @access  Private
router.post('/messages/:messageId/regenerate',
  authenticateToken,
  validateParams(z.object({
    messageId: z.string().cuid('Invalid message ID'),
  })),
  conversationController.regenerateResponse
);

// @route   DELETE /api/conversations/messages/:messageId
// @desc    Delete a message
// @access  Private
router.delete('/messages/:messageId',
  authenticateToken,
  validateParams(z.object({
    messageId: z.string().cuid('Invalid message ID'),
  })),
  conversationController.deleteMessage
);

// @route   GET /api/conversations
// @desc    Get user's conversations
// @access  Private
router.get('/',
  authenticateToken,
  parseNumericQuery,
  validateQuery(conversationFiltersSchema),
  conversationController.getConversations
);

// @route   POST /api/conversations
// @desc    Start a new conversation with a character
// @access  Private
router.post('/',
  authenticateToken,
  validateBody(createConversationSchema),
  conversationController.createConversation
);

// @route   GET /api/conversations/:id/stats
// @desc    Get conversation statistics
// @access  Private
router.get('/:id/stats',
  authenticateToken,
  validateParams(conversationIdSchema),
  conversationController.getConversationStats
);

// @route   GET /api/conversations/:id/messages
// @desc    Get messages for a conversation
// @access  Private
router.get('/:id/messages',
  authenticateToken,
  validateParams(conversationIdSchema),
  parseNumericQuery,
  validateQuery(messageFiltersSchema),
  conversationController.getMessages
);

// @route   POST /api/conversations/:id/messages
// @desc    Send a message in a conversation (REST fallback for WebSocket)
// @access  Private
router.post('/:id/messages',
  authenticateToken,
  chatRateLimiter,
  validateParams(conversationIdSchema),
  validateBody(sendMessageSchema),
  conversationController.sendMessage
);

// @route   PUT /api/conversations/:id/title
// @desc    Update conversation title
// @access  Private
router.put('/:id/title',
  authenticateToken,
  validateParams(conversationIdSchema),
  validateBody(z.object({
    title: z.string().max(100, 'Title must be less than 100 characters'),
  })),
  conversationController.updateConversationTitle
);

// @route   GET /api/conversations/:id
// @desc    Get conversation details
// @access  Private
router.get('/:id',
  authenticateToken,
  validateParams(conversationIdSchema),
  conversationController.getConversation
);

// @route   DELETE /api/conversations/:id
// @desc    Delete a conversation
// @access  Private
router.delete('/:id',
  authenticateToken,
  validateParams(conversationIdSchema),
  conversationController.deleteConversation
);



export { router as conversationRoutes };
import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  SparklesIcon, 
  StarIcon,
  ChatBubbleLeftRightIcon,
  FireIcon,
  UserGroupIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import { 
  StarIcon as StarIconSolid,
  FireIcon as FireIconSolid 
} from '@heroicons/react/24/solid'

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-10 left-10 w-20 h-20 bg-primary-500 rounded-full blur-xl animate-float"></div>
          <div className="absolute top-40 right-20 w-32 h-32 bg-secondary-400 rounded-full blur-2xl animate-pulse-slow"></div>
          <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-primary-400 rounded-full blur-lg animate-bounce-slow"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 animate-fade-in-up">
              <span className="text-gradient-lg">Meet your</span>
              <br />
              <span className="text-gray-900 dark:text-white">AI companions</span>
            </h1>
            <p className="text-base sm:text-lg lg:text-xl xl:text-2xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto mb-12 animate-fade-in-up px-4" style={{animationDelay: '0.2s'}}>
              Create unique AI characters with distinct personalities and engage in 
              meaningful conversations that feel natural and authentic.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-16 animate-fade-in-up px-4" style={{animationDelay: '0.4s'}}>
              <Link
                to="/create"
                className="btn-primary px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg group shadow-2xl hover:shadow-glow w-full sm:w-auto touch-manipulation"
              >
                <PlusIcon className="h-6 w-6 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                Create Character
              </Link>
              <Link
                to="/browse"
                className="btn-ghost px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg group w-full sm:w-auto touch-manipulation"
              >
                <MagnifyingGlassIcon className="h-6 w-6 mr-2 group-hover:scale-110 transition-transform duration-300" />
                Browse Characters
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto animate-fade-in-up" style={{animationDelay: '0.6s'}}>
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient">10K+</div>
                <div className="text-gray-600 dark:text-gray-400 font-medium">Characters Created</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient">50K+</div>
                <div className="text-gray-600 dark:text-gray-400 font-medium">Conversations</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient">2K+</div>
                <div className="text-gray-600 dark:text-gray-400 font-medium">Active Users</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Link
            to="/create"
            className="card-hover group bg-gradient-to-br from-primary-500 to-primary-600 text-white p-8"
          >
            <div className="flex items-center mb-4">
              <div className="p-3 bg-white/20 rounded-2xl group-hover:bg-white/30 transition-all duration-300">
                <PlusIcon className="h-8 w-8" />
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-2">Create Character</h3>
            <p className="text-primary-100 mb-4">
              Build your perfect AI companion with unique personality traits
            </p>
            <div className="flex items-center text-white font-semibold group-hover:translate-x-1 transition-transform duration-300">
              Get Started <ArrowRightIcon className="h-5 w-5 ml-2" />
            </div>
          </Link>

          <Link
            to="/browse"
            className="card-hover group bg-gradient-to-br from-secondary-500 to-secondary-600 text-white p-8"
          >
            <div className="flex items-center mb-4">
              <div className="p-3 bg-white/20 rounded-2xl group-hover:bg-white/30 transition-all duration-300">
                <MagnifyingGlassIcon className="h-8 w-8" />
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-2">Browse Characters</h3>
            <p className="text-secondary-100 mb-4">
              Discover thousands of amazing AI characters created by the community
            </p>
            <div className="flex items-center text-white font-semibold group-hover:translate-x-1 transition-transform duration-300">
              Explore Now <ArrowRightIcon className="h-5 w-5 ml-2" />
            </div>
          </Link>

          <div className="card-hover group bg-gradient-to-br from-gray-700 to-gray-800 dark:from-gray-600 dark:to-gray-700 text-white p-8">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-white/20 rounded-2xl group-hover:bg-white/30 transition-all duration-300">
                <SparklesIcon className="h-8 w-8" />
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-2">Getting Started</h3>
            <p className="text-gray-200 mb-4">
              Learn tips and tricks to create engaging AI characters
            </p>
            <div className="flex items-center text-white font-semibold group-hover:translate-x-1 transition-transform duration-300">
              Learn More <ArrowRightIcon className="h-5 w-5 ml-2" />
            </div>
          </div>
        </div>
      </div>

      {/* Featured Characters Section */}
      <div className="bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                <span className="text-gradient">Featured</span> Characters
              </h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg">
                Discover the most popular AI companions
              </p>
            </div>
            <Link
              to="/browse"
              className="btn-outline group hidden sm:flex"
            >
              View All
              <ArrowRightIcon className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="card-hover group">
                <div className="flex items-start mb-6">
                  <div className={`avatar-xl mr-4 ${
                    i === 1 ? 'from-blue-500 to-purple-600' :
                    i === 2 ? 'from-green-500 to-teal-600' :
                    'from-pink-500 to-red-600'
                  } bg-gradient-to-br`}>
                    AI
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                        {i === 1 ? 'Luna' : i === 2 ? 'Marcus' : 'Aria'}
                      </h3>
                      <div className="flex items-center">
                        <StarIconSolid className="h-4 w-4 text-yellow-400" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400 ml-1">
                          4.{8 + i}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                      {i === 1 ? 'Creative Assistant' : i === 2 ? 'Life Coach' : 'Storyteller'}
                    </p>
                    <div className="flex items-center text-xs text-gray-400">
                      <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                      <span>{1.2 + i * 0.3}k chats</span>
                      <span className="mx-2">•</span>
                      <FireIconSolid className="h-4 w-4 mr-1 text-orange-500" />
                      <span>Trending</span>
                    </div>
                  </div>
                </div>
                
                <p className="text-gray-600 dark:text-gray-300 mb-6 line-clamp-3">
                  {i === 1 ? 
                    'A creative and inspiring AI that helps you brainstorm ideas, write stories, and explore your imagination.' :
                    i === 2 ?
                    'A supportive life coach who provides motivation, guidance, and practical advice for personal growth.' :
                    'An enchanting storyteller who weaves magical tales and brings characters to life through vivid narratives.'
                  }
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    {[1,2,3,4,5].map((star) => (
                      <StarIconSolid 
                        key={star} 
                        className={`h-4 w-4 ${
                          star <= 4 + i ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                  <button className="btn-primary text-sm px-4 py-2 group-hover:scale-105 transition-transform duration-300">
                    <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                    Chat Now
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              to="/browse"
              className="btn-outline sm:hidden"
            >
              View All Characters
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* Recent Activity Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Your <span className="text-gradient">Journey</span> Begins
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
            Start your first conversation and discover the magic of AI companions
          </p>
        </div>
        
        <div className="card max-w-2xl mx-auto text-center">
          <div className="mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
              <UserGroupIcon className="h-10 w-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              No conversations yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Your recent conversations will appear here. Create your first character or browse existing ones to get started!
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4">
              <Link to="/create" className="btn-primary active:scale-95 transition-all duration-200">
                <PlusIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                <span className="hidden xs:inline">Create First Character</span>
                <span className="xs:hidden">Create Character</span>
              </Link>
              <Link to="/browse" className="btn-ghost active:scale-95 transition-all duration-200">
                <MagnifyingGlassIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                <span className="hidden xs:inline">Browse Characters</span>
                <span className="xs:hidden">Browse</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage
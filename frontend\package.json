{"name": "character-ai-frontend", "version": "1.0.0", "description": "Frontend for Character.AI clone application", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^2.0.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "socket.io-client": "^4.7.4", "zod": "^3.22.4"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.0"}, "keywords": ["react", "typescript", "vite", "character-ai", "chat"], "author": "Character AI <PERSON>", "license": "MIT"}
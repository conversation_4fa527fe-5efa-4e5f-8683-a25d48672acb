import { logger } from '@/utils/logger';
import { AppError } from '@/middleware/errorHandler';
import { PersonalityTraits } from '@/types/character';
import { getConfig } from '@/config/services';
import { HelpingAI } from 'helpingai';

// AI service with support for both mock and production implementations

export interface GenerateResponseInput {
  character: {
    id: string;
    name: string;
    personality: PersonalityTraits;
  };
  conversation: Array<{
    role: 'user' | 'assistant';
    content: string;
    createdAt: Date;
  }>;
  userMessage: string;
  userProfile?: {
    username: string;
    displayName: string;
  };
}

export interface ModerationResult {
  flagged: boolean;
  categories: {
    hate: boolean;
    harassment: boolean;
    selfHarm: boolean;
    sexual: boolean;
    violence: boolean;
  };
  reason?: string;
}

export class AIService {
  private helpingAIClient: HelpingAI | null = null;
  private config = getConfig();

  constructor() {
    // Initialize HelpingAI client if not in mock mode
    if (this.config.ai.provider === 'helpingai' && this.config.ai.apiKey) {
      try {
        this.helpingAIClient = new HelpingAI({
          apiKey: this.config.ai.apiKey,
          timeout: this.config.ai.timeout
        });
        logger.info('HelpingAI client initialized successfully');
      } catch (error) {
        logger.error('Failed to initialize HelpingAI client:', error);
        this.helpingAIClient = null;
      }
    }
  }

  // Generate character response (supports both mock and production)
  async generateResponse(input: GenerateResponseInput): Promise<string> {
    try {
      const { character, userMessage } = input;

      // First, moderate the user's message
      const moderation = await this.moderateContent(userMessage);
      if (moderation.flagged) {
        throw new AppError(`Message violates content policy: ${moderation.reason}`, 400);
      }

      let response: string;

      if (this.config.ai.provider === 'helpingai' && this.helpingAIClient) {
        response = await this.generateHelpingAIResponse(input);
      } else {
        response = await this.generateMockResponse(input);
      }

      // Moderate the AI response as well
      const responseModeration = await this.moderateContent(response);
      if (responseModeration.flagged) {
        logger.warn(`AI generated flagged content for character ${character.id}:`, responseModeration);
        return "I apologize, but I'm having trouble formulating an appropriate response. Could you try rephrasing your message?";
      }

      return response;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('AI generation error:', error);
      throw new AppError('Failed to generate AI response', 500);
    }
  }

  // Generate response using HelpingAI API
  private async generateHelpingAIResponse(input: GenerateResponseInput): Promise<string> {
    if (!this.helpingAIClient) {
      throw new AppError('HelpingAI client not initialized', 500);
    }

    const { character, conversation, userMessage, userProfile } = input;
    const startTime = Date.now();

    try {
      // Build the conversation history for context
      const messages = [
        {
          role: 'system' as const,
          content: this.buildCharacterPrompt(character, userProfile)
        },
        // Add recent conversation history (last 10 messages for context)
        ...conversation.slice(-10).map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        })),
        {
          role: 'user' as const,
          content: userMessage
        }
      ];

      const response = await this.helpingAIClient.chat.completions.create({
        model: this.config.ai.model,
        messages,
        max_tokens: this.config.ai.maxTokens,
        temperature: 0.7,
        top_p: 0.9
      });

      const responseTime = Date.now() - startTime;
      const content = response.choices[0]?.message?.content;

      if (!content) {
        throw new AppError('Empty response from HelpingAI', 500);
      }

      logger.info(`HelpingAI response generated for character ${character.name} in ${responseTime}ms`);
      return content;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error(`HelpingAI API error (${responseTime}ms):`, error);

      // Fallback to mock response on API failure
      logger.warn('Falling back to mock response due to HelpingAI API error');
      return this.generateMockResponse(input);
    }
  }

  // Generate mock response for testing/fallback
  private async generateMockResponse(input: GenerateResponseInput): Promise<string> {
    const { character, userMessage } = input;

    // Simulate AI response generation with a delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Generate a mock response based on the character's personality
    const mockResponses = [
      `Hello! I'm ${character.name}. Thanks for your message: "${userMessage}". How can I help you today?`,
      `Interesting point! As ${character.name}, I find that fascinating. What else would you like to discuss?`,
      `That's a great question! In my experience as ${character.name}, I've found that ${userMessage} is quite intriguing.`,
      `I appreciate you sharing that with me. As ${character.name}, I think we should explore this topic further.`,
      `Thanks for reaching out! I'm ${character.name}, and I'm here to help with whatever you need.`
    ];

    // Select a random response
    const response = mockResponses[Math.floor(Math.random() * mockResponses.length)];

    logger.info(`Mock AI response generated for character ${character.name}`);
    return response;
  }

  // Build character-specific prompt for HelpingAI
  private buildCharacterPrompt(character: { name: string; personality: PersonalityTraits }, userProfile?: { username: string; displayName: string }): string {
    const { name, personality } = character;
    const { traits, communicationStyle, background, goals, quirks } = personality;

    let prompt = `You are ${name}, an AI character with the following personality and characteristics:

PERSONALITY TRAITS: ${traits.join(', ')}

BACKGROUND: ${background}

COMMUNICATION STYLE: ${communicationStyle}`;

    if (goals && goals.length > 0) {
      prompt += `\n\nGOALS: ${goals.join(', ')}`;
    }

    if (quirks && quirks.length > 0) {
      prompt += `\n\nQUIRKS: ${quirks.join(', ')}`;
    }

    if (userProfile) {
      prompt += `\n\nYou are talking to ${userProfile.displayName} (username: ${userProfile.username}).`;
    }

    prompt += `\n\nPlease respond in character, maintaining your personality traits and communication style. Be engaging, helpful, and stay true to your character's background and goals.`;

    return prompt;
  }

  // Health check for AI service
  async healthCheck(): Promise<{ available: boolean; provider: string; models?: string[] }> {
    try {
      if (this.config.ai.provider === 'mock') {
        return {
          available: true,
          provider: 'mock',
          models: ['mock-model-1', 'mock-model-2']
        };
      }

      if (!this.helpingAIClient) {
        return {
          available: false,
          provider: 'helpingai',
          models: []
        };
      }

      // Try to list models to check if the API is working
      const modelList = await this.helpingAIClient.models.list();
      const models = modelList.data.map(model => model.id);

      return {
        available: true,
        provider: 'helpingai',
        models
      };
    } catch (error) {
      logger.error('AI service health check failed:', error);
      return {
        available: false,
        provider: this.config.ai.provider,
        models: []
      };
    }
  }

  // Estimate token count (simple approximation)
  estimateTokens(text: string): number {
    // Simple approximation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  // Cleanup resources
  async cleanup(): Promise<void> {
    if (this.helpingAIClient) {
      try {
        await this.helpingAIClient.cleanup();
        logger.info('HelpingAI client cleaned up');
      } catch (error) {
        logger.error('Error cleaning up HelpingAI client:', error);
      }
    }
  }

  // Moderate content using keyword-based or AI-based filtering
  async moderateContent(content: string): Promise<ModerationResult> {
    try {
      if (this.config.moderation.provider === 'helpingai' && this.helpingAIClient) {
        return await this.moderateWithHelpingAI(content);
      } else {
        return await this.moderateWithKeywords(content);
      }
    } catch (error) {
      logger.error('Content moderation error:', error);
      // Fallback to keyword moderation on error
      return await this.moderateWithKeywords(content);
    }
  }

  // HelpingAI-based content moderation
  private async moderateWithHelpingAI(content: string): Promise<ModerationResult> {
    if (!this.helpingAIClient) {
      throw new Error('HelpingAI client not available for moderation');
    }

    try {
      const moderationPrompt = `Please analyze the following content for harmful material. Respond with a JSON object containing:
- flagged: boolean (true if content violates policies)
- categories: object with boolean values for hate, harassment, selfHarm, sexual, violence
- reason: string (explanation if flagged)

Content to analyze: "${content}"

Respond only with the JSON object, no additional text.`;

      const response = await this.helpingAIClient.chat.completions.create({
        model: this.config.ai.model,
        messages: [{ role: 'user', content: moderationPrompt }],
        max_tokens: 200,
        temperature: 0.1 // Low temperature for consistent moderation
      });

      const responseContent = response.choices[0]?.message?.content;
      if (!responseContent) {
        throw new Error('Empty moderation response');
      }

      // Try to parse the JSON response
      const moderationResult = JSON.parse(responseContent.trim());

      // Validate the response structure
      if (typeof moderationResult.flagged !== 'boolean' || !moderationResult.categories) {
        throw new Error('Invalid moderation response format');
      }

      logger.debug('HelpingAI moderation completed:', { flagged: moderationResult.flagged });
      return moderationResult;
    } catch (error) {
      logger.error('HelpingAI moderation failed:', error);
      // Fallback to keyword moderation
      logger.warn('Falling back to keyword-based moderation');
      return await this.moderateWithKeywords(content);
    }
  }

  // Keyword-based content moderation (fallback)
  private async moderateWithKeywords(content: string): Promise<ModerationResult> {
    // Simple keyword-based moderation
    const harmfulKeywords = [
      // Hate speech
      'hate', 'nazi', 'terrorist', 'kill yourself', 'kys',
      // Harassment
      'harass', 'bully', 'stalk', 'doxx',
      // Self-harm
      'suicide', 'self harm', 'cut myself', 'end my life',
      // Sexual content (basic)
      'explicit sexual', 'pornographic', 'nude pics',
      // Violence
      'murder', 'bomb', 'weapon', 'violence', 'attack'
    ];

    const lowerContent = content.toLowerCase();
    const flaggedCategories: string[] = [];
    let flagged = false;

    // Check for harmful keywords
    for (const keyword of harmfulKeywords) {
      if (lowerContent.includes(keyword)) {
        flagged = true;
        // Add category based on keyword
        if (keyword.includes('hate') || keyword.includes('nazi') || keyword.includes('terrorist')) {
          flaggedCategories.push('hate');
        } else if (keyword.includes('harass') || keyword.includes('bully') || keyword.includes('stalk')) {
          flaggedCategories.push('harassment');
        } else if (keyword.includes('suicide') || keyword.includes('self harm') || keyword.includes('cut myself')) {
          flaggedCategories.push('selfHarm');
        } else if (keyword.includes('sexual') || keyword.includes('pornographic')) {
          flaggedCategories.push('sexual');
        } else if (keyword.includes('murder') || keyword.includes('bomb') || keyword.includes('weapon')) {
          flaggedCategories.push('violence');
        }
      }
    }

    return {
      flagged,
      categories: {
        hate: flaggedCategories.includes('hate'),
        harassment: flaggedCategories.includes('harassment'),
        selfHarm: flaggedCategories.includes('selfHarm'),
        sexual: flaggedCategories.includes('sexual'),
        violence: flaggedCategories.includes('violence'),
      },
      reason: flagged ? `Content flagged for: ${flaggedCategories.join(', ')}` : undefined,
    };
  }

  // Estimate token count (simplified)
  estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }
}

export default AIService;
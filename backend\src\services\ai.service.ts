import { logger } from '@/utils/logger';
import { AppError } from '@/middleware/errorHandler';
import { PersonalityTraits } from '@/types/character';

// Mock AI service for local testing
// This version simulates AI responses without requiring external APIs

export interface GenerateResponseInput {
  character: {
    id: string;
    name: string;
    personality: PersonalityTraits;
  };
  conversation: Array<{
    role: 'user' | 'assistant';
    content: string;
    createdAt: Date;
  }>;
  userMessage: string;
  userProfile?: {
    username: string;
    displayName: string;
  };
}

export interface ModerationResult {
  flagged: boolean;
  categories: {
    hate: boolean;
    harassment: boolean;
    selfHarm: boolean;
    sexual: boolean;
    violence: boolean;
  };
  reason?: string;
}

export class AIService {
  // Generate character response (mock version)
  async generateResponse(input: GenerateResponseInput): Promise<string> {
    try {
      const { character, userMessage } = input;

      // First, moderate the user's message
      const moderation = await this.moderateContent(userMessage);
      if (moderation.flagged) {
        throw new AppError(`Message violates content policy: ${moderation.reason}`, 400);
      }

      // Simulate AI response generation with a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate a mock response based on the character's personality
      const mockResponses = [
        `Hello! I'm ${character.name}. Thanks for your message: "${userMessage}". How can I help you today?`,
        `Interesting point! As ${character.name}, I find that fascinating. What else would you like to discuss?`,
        `That's a great question! In my experience as ${character.name}, I've found that ${userMessage} is quite intriguing.`,
        `I appreciate you sharing that with me. As ${character.name}, I think we should explore this topic further.`,
        `Thanks for reaching out! I'm ${character.name}, and I'm here to help with whatever you need.`
      ];

      // Select a random response
      const response = mockResponses[Math.floor(Math.random() * mockResponses.length)];

      // Moderate the AI response as well
      const responseModeration = await this.moderateContent(response);
      if (responseModeration.flagged) {
        logger.warn(`Mock AI generated flagged content for character ${character.id}:`, responseModeration);
        return "I apologize, but I'm having trouble formulating an appropriate response. Could you try rephrasing your message?";
      }

      logger.info(`Mock AI response generated for character ${character.name}`);
      return response;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Mock AI generation error:', error);
      throw new AppError('Failed to generate AI response', 500);
    }
  }

  // Build character-specific prompt (simplified for mock)
  private buildCharacterPrompt(character: { name: string; personality: PersonalityTraits }, userProfile?: { username: string; displayName: string }): string {
    const { name, personality } = character;
    const { traits, communicationStyle, background } = personality;

    return `You are ${name}, an AI character with traits: ${traits.join(', ')}. Background: ${background}. Communication style: ${communicationStyle}.`;
  }

  // Moderate content using simple keyword-based filtering
  async moderateContent(content: string): Promise<ModerationResult> {
    try {
      // Simple keyword-based moderation
      const harmfulKeywords = [
        // Hate speech
        'hate', 'nazi', 'terrorist', 'kill yourself', 'kys',
        // Harassment
        'harass', 'bully', 'stalk', 'doxx',
        // Self-harm
        'suicide', 'self harm', 'cut myself', 'end my life',
        // Sexual content (basic)
        'explicit sexual', 'pornographic', 'nude pics',
        // Violence
        'murder', 'bomb', 'weapon', 'violence', 'attack'
      ];

      const lowerContent = content.toLowerCase();
      const flaggedCategories: string[] = [];
      let flagged = false;

      // Check for harmful keywords
      for (const keyword of harmfulKeywords) {
        if (lowerContent.includes(keyword)) {
          flagged = true;
          // Add category based on keyword
          if (keyword.includes('hate') || keyword.includes('nazi') || keyword.includes('terrorist')) {
            flaggedCategories.push('hate');
          } else if (keyword.includes('harass') || keyword.includes('bully') || keyword.includes('stalk')) {
            flaggedCategories.push('harassment');
          } else if (keyword.includes('suicide') || keyword.includes('self harm') || keyword.includes('cut myself')) {
            flaggedCategories.push('selfHarm');
          } else if (keyword.includes('sexual') || keyword.includes('pornographic')) {
            flaggedCategories.push('sexual');
          } else if (keyword.includes('murder') || keyword.includes('bomb') || keyword.includes('weapon')) {
            flaggedCategories.push('violence');
          }
        }
      }

      return {
        flagged,
        categories: {
          hate: flaggedCategories.includes('hate'),
          harassment: flaggedCategories.includes('harassment'),
          selfHarm: flaggedCategories.includes('selfHarm'),
          sexual: flaggedCategories.includes('sexual'),
          violence: flaggedCategories.includes('violence'),
        },
        reason: flagged ? `Content flagged for: ${flaggedCategories.join(', ')}` : undefined,
      };
    } catch (error) {
      logger.error('Content moderation error:', error);
      return {
        flagged: false,
        categories: {
          hate: false,
          harassment: false,
          selfHarm: false,
          sexual: false,
          violence: false,
        },
      };
    }
  }

  // Estimate token count (simplified)
  estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }
}

export default AIService;
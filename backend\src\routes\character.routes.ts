import { Router } from 'express';
import { authenticateToken, optionalAuth } from '@/middleware/auth';
import { characterCreationRateLimiter } from '@/middleware/rateLimiter';
import { validateBody, validateParams } from '@/middleware/validation';
import {
  createCharacterSchema,
  updateCharacterSchema,
  characterIdSchema,
} from '@/schemas/character.schemas';
import characterController from '@/controllers/character.controller';

const router = Router();

// @route   GET /api/characters
// @desc    Get characters with search and filtering
// @access  Public
router.get('/',
  optionalAuth,
  characterController.getCharacters
);

// @route   POST /api/characters
// @desc    Create a new character
// @access  Private
router.post('/',
  authenticateToken,
  characterCreationRateLimiter,
  validateBody(createCharacterSchema),
  characterController.createCharacter
);

// @route   GET /api/characters/:id
// @desc    Get character by ID
// @access  Public
router.get('/:id',
  optionalAuth,
  validateParams(characterIdSchema),
  characterController.getCharacter
);

// @route   PUT /api/characters/:id
// @desc    Update character (owner only)
// @access  Private
router.put('/:id',
  authenticateToken,
  validateParams(characterIdSchema),
  validateBody(updateCharacterSchema),
  characterController.updateCharacter
);

// @route   DELETE /api/characters/:id
// @desc    Delete character (owner only)
// @access  Private
router.delete('/:id',
  authenticateToken,
  validateParams(characterIdSchema),
  characterController.deleteCharacter
);

export { router as characterRoutes };
import React from 'react'
import { UserCircleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline'

const ProfilePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="text-center mb-6 sm:mb-8">
          <div className="flex justify-center mb-4 sm:mb-6">
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl sm:rounded-3xl flex items-center justify-center animate-float">
              <UserCircleIcon className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
            </div>
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">
            User <span className="text-gradient">Profile</span>
          </h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 px-4">
            Manage your account settings and preferences.
          </p>
        </div>
        
        {/* Placeholder for Profile Content */}
        <div className="card max-w-2xl mx-auto text-center">
          <div className="mb-6">
            <Cog6ToothIcon className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-2">
              Profile Settings
            </h3>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-6">
              User profile and settings interface will be implemented here.
            </p>
            <button className="btn-primary px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base active:scale-95 transition-all duration-200">
              <Cog6ToothIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
              Manage Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfilePage
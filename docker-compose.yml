version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: characterai_postgres
    environment:
      POSTGRES_DB: characterai_dev
      POSTGRES_USER: characterai
      POSTGRES_PASSWORD: characterai_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U characterai -d characterai_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: characterai_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Database
  postgres_test:
    image: postgres:15
    container_name: characterai_postgres_test
    environment:
      POSTGRES_DB: characterai_test
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    profiles:
      - test

  # Backend Application (for development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: characterai_backend
    environment:
      NODE_ENV: development
      DATABASE_URL: ***********************************************************/characterai_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret-key
      JWT_REFRESH_SECRET: dev-refresh-secret-key
      HELPINGAI_API_KEY: ${HELPINGAI_API_KEY}
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    profiles:
      - dev

volumes:
  postgres_data:
  redis_data:
  postgres_test_data:
import React, { useState } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'
import {
  PlusIcon,
  XMarkIcon,
  PhotoIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline'
import { useCreateCharacterMutation } from '../../store/api/characterApi'
import type { CreateCharacterInput } from '../../types'

type CharacterFormData = {
  name: string
  description: string
  avatarUrl?: string
  personality: {
    traits: string[]
    communicationStyle: 'casual' | 'formal' | 'playful' | 'professional' | 'poetic'
    background: string
    goals: string[]
    quirks: string[]
  }
  tags: string[]
  isPublic: boolean
}

const COMMUNICATION_STYLES = [
  { value: 'casual', label: 'Casual', description: 'Relaxed and informal conversation' },
  { value: 'formal', label: 'Formal', description: 'Professional and structured communication' },
  { value: 'playful', label: 'Playful', description: 'Fun and lighthearted interactions' },
  { value: 'professional', label: 'Professional', description: 'Business-oriented and focused' },
  { value: 'poetic', label: 'Poetic', description: 'Artistic and expressive language' },
] as const

const CharacterCreationForm: React.FC = () => {
  const navigate = useNavigate()
  const [createCharacter, { isLoading }] = useCreateCharacterMutation()
  const [currentStep, setCurrentStep] = useState(1)
  const [showPreview, setShowPreview] = useState(false)
  
  // Form state with react-hook-form
  const {
    register,
    control,
    watch,
    formState: { errors },
    setValue,
    handleSubmit,
  } = useForm<CharacterFormData>({
    mode: 'onChange',
    defaultValues: {
      name: '',
      description: '',
      avatarUrl: '',
      personality: {
        traits: ['Friendly'],
        communicationStyle: 'casual',
        background: '',
        goals: ['Have meaningful conversations'],
        quirks: [],
      },
      tags: ['General'],
      isPublic: true,
    },
  })

  // Watch form data for preview
  const watchedData = watch()
  
  // Simple state for managing dynamic arrays
  const [traitCount, setTraitCount] = useState(1)
  const [goalCount, setGoalCount] = useState(1)
  const [quirkCount, setQuirkCount] = useState(0)
  const [tagCount, setTagCount] = useState(1)

  const handleFormSubmit = handleSubmit(async (formData) => {
    try {
      console.log('Form submitted with data:', formData);
      
      // Validate that we have non-empty required fields
      const validationErrors = [];
      
      if (!formData.name?.trim()) validationErrors.push('Character name is required');
      if (!formData.description?.trim()) validationErrors.push('Description is required');
      if (!formData.personality.background?.trim()) validationErrors.push('Background story is required');
      
      const nonEmptyTraits = formData.personality.traits.filter(trait => trait.trim() !== '');
      if (nonEmptyTraits.length === 0) validationErrors.push('At least one personality trait is required');
      
      const nonEmptyGoals = formData.personality.goals.filter(goal => goal.trim() !== '');
      if (nonEmptyGoals.length === 0) validationErrors.push('At least one goal is required');
      
      const nonEmptyTags = formData.tags.filter(tag => tag.trim() !== '');
      if (nonEmptyTags.length === 0) validationErrors.push('At least one tag is required');
      
      // Validate avatar URL if provided
      if (formData.avatarUrl && formData.avatarUrl.trim() !== '') {
        try {
          new URL(formData.avatarUrl);
        } catch {
          validationErrors.push('Please provide a valid avatar URL');
        }
      }
      
      if (validationErrors.length > 0) {
        console.error('Validation errors:', validationErrors);
        toast.error(`Please fix the following errors: ${validationErrors.join(', ')}`);
        return;
      }
      
      // Clean up empty arrays and strings
      const cleanedData: CreateCharacterInput = {
        ...formData,
        avatarUrl: formData.avatarUrl?.trim() || undefined,
        personality: {
          ...formData.personality,
          traits: nonEmptyTraits,
          goals: nonEmptyGoals,
          quirks: formData.personality.quirks?.filter(quirk => quirk.trim() !== '') || [],
        },
        tags: nonEmptyTags,
      };
      
      console.log('Cleaned data:', cleanedData);
      console.log('About to call createCharacter API...');

      const result = await createCharacter(cleanedData).unwrap();
      
      console.log('API result:', result);
      
      if (result.success && result.data) {
        toast.success('Character created successfully!')
        navigate('/')
      }
    } catch (error: any) {
      console.error('Character creation error:', error);
      const errorMessage = error?.data?.error?.message || 'Failed to create character'
      toast.error(errorMessage)
    }
  })

  // Remove the old manual helper functions since we're using useFieldArray now

  const nextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, 4))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3, 4].map((step) => (
        <div key={step} className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= step
                ? 'bg-primary-600 text-white'
                : 'bg-gray-200 text-gray-600'
            }`}
          >
            {step}
          </div>
          {step < 4 && (
            <div
              className={`w-16 h-1 ${
                currentStep > step ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            />
          )}
        </div>
      ))}
    </div>
  )

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Basic Information</h2>
        <p className="text-gray-600">Let's start with the basics about your character</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Character Name *
          </label>
          <input
            {...register('name')}
            type="text"
            className="input"
            placeholder="Enter character name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        <div className="md:col-span-2">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            {...register('description')}
            rows={4}
            className="input"
            placeholder="Describe your character in detail..."
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
          )}
        </div>

        <div className="md:col-span-2">
          <label htmlFor="avatarUrl" className="block text-sm font-medium text-gray-700 mb-2">
            Avatar URL (Optional)
          </label>
          <div className="flex">
            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
              <PhotoIcon className="h-4 w-4" />
            </span>
            <input
              {...register('avatarUrl')}
              type="url"
              className="input rounded-l-none"
              placeholder="https://example.com/avatar.jpg"
            />
          </div>
          {errors.avatarUrl && (
            <p className="mt-1 text-sm text-red-600">{errors.avatarUrl.message}</p>
          )}
        </div>

        <div className="md:col-span-2">
          <div className="flex items-center">
            <input
              {...register('isPublic')}
              type="checkbox"
              className="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
            />
            <label className="ml-2 block text-sm text-gray-700">
              Make this character public
              <span className="text-gray-500 text-xs block">
                Other users can discover and chat with your character
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  )

  const renderPersonality = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Personality Traits</h2>
        <p className="text-gray-600">Define your character's personality and traits</p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Personality Traits *
          </label>
          <div className="space-y-2">
            {Array.from({ length: traitCount }, (_, index) => (
              <div key={index} className="flex">
                <input
                  {...register(`personality.traits.${index}` as const)}
                  type="text"
                  className="input"
                  placeholder="e.g., friendly, intelligent, mysterious"
                />
                {traitCount > 1 && (
                  <button
                    type="button"
                    onClick={() => {
                      setValue(`personality.traits.${index}`, '')
                      setTraitCount(Math.max(1, traitCount - 1))
                    }}
                    className="ml-2 p-2 text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            ))}
            {traitCount < 20 && (
              <button
                type="button"
                onClick={() => setTraitCount(traitCount + 1)}
                className="flex items-center text-sm text-primary-600 hover:text-primary-800"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add trait
              </button>
            )}
          </div>
          {errors.personality?.traits && (
            <p className="mt-1 text-sm text-red-600">{errors.personality.traits.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Communication Style *
          </label>
          <Controller
            name="personality.communicationStyle"
            control={control}
            render={({ field }) => (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {COMMUNICATION_STYLES.map((style) => (
                  <label
                    key={style.value}
                    className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                      field.value === style.value
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <input
                      type="radio"
                      {...field}
                      value={style.value}
                      className="sr-only"
                    />
                    <div className="font-medium text-sm">{style.label}</div>
                    <div className="text-xs text-gray-600">{style.description}</div>
                  </label>
                ))}
              </div>
            )}
          />
          {errors.personality?.communicationStyle && (
            <p className="mt-1 text-sm text-red-600">{errors.personality.communicationStyle.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="background" className="block text-sm font-medium text-gray-700 mb-2">
            Background Story *
          </label>
          <textarea
            {...register('personality.background')}
            rows={6}
            className="input"
            placeholder="Tell the backstory of your character. What's their history, where do they come from, what shaped them?"
          />
          {errors.personality?.background && (
            <p className="mt-1 text-sm text-red-600">{errors.personality.background.message}</p>
          )}
        </div>
      </div>
    </div>
  )

  const renderGoalsAndQuirks = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Goals & Quirks</h2>
        <p className="text-gray-600">What drives your character and makes them unique?</p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Goals *
          </label>
          <div className="space-y-2">
            {Array.from({ length: goalCount }, (_, index) => (
              <div key={index} className="flex">
                <input
                  {...register(`personality.goals.${index}` as const)}
                  type="text"
                  className="input"
                  placeholder="e.g., help others, learn new things, find love"
                />
                {goalCount > 1 && (
                  <button
                    type="button"
                    onClick={() => {
                      setValue(`personality.goals.${index}`, '')
                      setGoalCount(Math.max(1, goalCount - 1))
                    }}
                    className="ml-2 p-2 text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            ))}
            {goalCount < 10 && (
              <button
                type="button"
                onClick={() => setGoalCount(goalCount + 1)}
                className="flex items-center text-sm text-primary-600 hover:text-primary-800"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add goal
              </button>
            )}
          </div>
          {errors.personality?.goals && (
            <p className="mt-1 text-sm text-red-600">{errors.personality.goals.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Quirks (Optional)
          </label>
          <div className="space-y-2">
            {Array.from({ length: Math.max(1, quirkCount) }, (_, index) => (
              <div key={index} className="flex">
                <input
                  {...register(`personality.quirks.${index}` as const)}
                  type="text"
                  className="input"
                  placeholder="e.g., always carries a lucky charm, speaks in rhymes"
                />
                <button
                  type="button"
                  onClick={() => {
                    setValue(`personality.quirks.${index}`, '')
                    setQuirkCount(Math.max(0, quirkCount - 1))
                  }}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
            ))}
            {quirkCount < 10 && (
              <button
                type="button"
                onClick={() => setQuirkCount(quirkCount + 1)}
                className="flex items-center text-sm text-primary-600 hover:text-primary-800"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add quirk
              </button>
            )}
          </div>
          {errors.personality?.quirks && (
            <p className="mt-1 text-sm text-red-600">{errors.personality.quirks.message}</p>
          )}
        </div>
      </div>
    </div>
  )

  const renderTagsAndFinish = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Tags & Review</h2>
        <p className="text-gray-600">Add tags to help others discover your character</p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tags *
          </label>
          <div className="space-y-2">
            {Array.from({ length: tagCount }, (_, index) => (
              <div key={index} className="flex">
                <input
                  {...register(`tags.${index}` as const)}
                  type="text"
                  className="input"
                  placeholder="e.g., fantasy, helpful, funny, romance"
                />
                {tagCount > 1 && (
                  <button
                    type="button"
                    onClick={() => {
                      setValue(`tags.${index}`, '')
                      setTagCount(Math.max(1, tagCount - 1))
                    }}
                    className="ml-2 p-2 text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            ))}
            {tagCount < 10 && (
              <button
                type="button"
                onClick={() => setTagCount(tagCount + 1)}
                className="flex items-center text-sm text-primary-600 hover:text-primary-800"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add tag
              </button>
            )}
          </div>
          {errors.tags && (
            <p className="mt-1 text-sm text-red-600">{errors.tags.message}</p>
          )}
        </div>

        <div className="border rounded-lg p-4 bg-gray-50">
          <button
            type="button"
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center text-sm font-medium text-gray-700 mb-2"
          >
            <InformationCircleIcon className="h-4 w-4 mr-1" />
            {showPreview ? 'Hide' : 'Show'} Character Preview
          </button>
          
          {showPreview && (
            <div className="text-sm text-gray-600 space-y-2">
              <div><strong>Name:</strong> {watchedData.name || 'Unnamed Character'}</div>
              <div><strong>Description:</strong> {watchedData.description?.substring(0, 100) || 'No description'}...</div>
              <div><strong>Communication Style:</strong> {watchedData.personality?.communicationStyle || 'Not set'}</div>
              <div><strong>Traits:</strong> {watchedData.personality?.traits?.filter((t: string) => t).join(', ') || 'None'}</div>
              <div><strong>Goals:</strong> {watchedData.personality?.goals?.filter((g: string) => g).join(', ') || 'None'}</div>
              <div><strong>Tags:</strong> {watchedData.tags?.filter((t: string) => t).join(', ') || 'None'}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInfo()
      case 2:
        return renderPersonality()
      case 3:
        return renderGoalsAndQuirks()
      case 4:
        return renderTagsAndFinish()
      default:
        return renderBasicInfo()
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {renderStepIndicator()}
      
      <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
        {renderStepContent()}
      </div>

      <div className="flex justify-between">
        <button
          type="button"
          onClick={prevStep}
          disabled={currentStep === 1}
          className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        
        <div className="flex space-x-4">
          {currentStep < 4 ? (
            <button
              type="button"
              onClick={nextStep}
              className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Next
            </button>
          ) : (
            <button
              type="button"
              disabled={isLoading}
              onClick={handleFormSubmit}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Creating...' : 'Create Character'}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default CharacterCreationForm
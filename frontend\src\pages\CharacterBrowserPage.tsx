import React, { useState } from 'react'
import { 
  MagnifyingGlassIcon, 
  AdjustmentsHorizontalIcon,
  StarIcon,
  ChatBubbleLeftRightIcon,
  FireIcon,
  UserGroupIcon,
  HeartIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'
import { 
  StarIcon as StarIconSolid,
  FireIcon as FireIconSolid,
  HeartIcon as HeartIconSolid
} from '@heroicons/react/24/solid'

const CharacterBrowserPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('popular')
  const [showFilters, setShowFilters] = useState(false)

  const categories = [
    { id: 'all', name: 'All', count: 1234 },
    { id: 'anime', name: 'Anime', count: 456 },
    { id: 'movies', name: 'Movies', count: 234 },
    { id: 'games', name: 'Games', count: 189 },
    { id: 'books', name: 'Books', count: 167 },
    { id: 'celebrities', name: 'Celebrities', count: 123 },
    { id: 'original', name: 'Original', count: 89 }
  ]

  const sortOptions = [
    { id: 'popular', name: 'Most Popular' },
    { id: 'recent', name: 'Recently Added' },
    { id: 'rating', name: 'Highest Rated' },
    { id: 'chats', name: 'Most Chatted' }
  ]

  // Mock character data
  const characters = Array.from({ length: 12 }, (_, i) => ({
    id: i + 1,
    name: ['Luna', 'Marcus', 'Aria', 'Zara', 'Alex', 'Maya', 'Kai', 'Nova', 'Sage', 'Rio', 'Iris', 'Orion'][i],
    category: ['anime', 'movies', 'original', 'games', 'books', 'celebrities'][i % 6],
    description: 'A fascinating AI character with unique personality traits and engaging conversation abilities.',
    rating: 4.5 + (i % 5) * 0.1,
    chats: (1200 + i * 230).toLocaleString(),
    avatar: `from-${ ['blue', 'purple', 'pink', 'green', 'orange', 'teal'][i % 6] }-500 to-${ ['purple', 'pink', 'red', 'teal', 'red', 'blue'][i % 6] }-600`,
    isLiked: i % 3 === 0,
    isTrending: i % 4 === 0
  }))

  const filteredCharacters = characters.filter(char => {
    const matchesSearch = char.name.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || char.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2">
            Browse <span className="text-gradient">Characters</span>
          </h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
            Discover amazing AI companions created by our community
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 sm:mb-8">
          {/* Search Bar */}
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search characters..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-12 py-3 sm:py-4 border border-gray-200 dark:border-gray-700 rounded-xl sm:rounded-2xl bg-white/80 dark:bg-gray-800/80 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200"
            />
            <button 
              onClick={() => setShowFilters(!showFilters)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 md:hidden"
            >
              <FunnelIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Desktop Filters */}
          <div className="hidden md:flex flex-wrap items-center gap-4">
            {/* Categories */}
            <div className="flex flex-wrap items-center gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl text-sm font-medium transition-all duration-200 touch-manipulation ${
                    selectedCategory === category.id
                      ? 'bg-primary-500 text-white shadow-lg'
                      : 'bg-white/50 dark:bg-gray-800/50 text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800'
                  }`}
                >
                  {category.name}
                  <span className="ml-1 text-xs opacity-75">({category.count})</span>
                </button>
              ))}
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 sm:px-4 py-2 bg-white/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg sm:rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500/20 transition-all duration-200"
            >
              {sortOptions.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.name}
                </option>
              ))}
            </select>
          </div>

          {/* Mobile Filters */}
          {showFilters && (
            <div className="md:hidden bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 mt-4 animate-fade-in-up">
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Categories</h3>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 touch-manipulation ${
                        selectedCategory === category.id
                          ? 'bg-primary-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      {category.name} ({category.count})
                    </button>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Sort by</h3>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-700 dark:text-gray-300"
                >
                  {sortOptions.map((option) => (
                    <option key={option.id} value={option.id}>
                      {option.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Results Count */}
        <div className="mb-4 sm:mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Showing {filteredCharacters.length} characters
            {searchQuery && (
              <span> for "{searchQuery}"</span>
            )}
          </p>
        </div>

        {/* Character Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {filteredCharacters.map((character) => (
            <div
              key={character.id}
              className="card-hover group active:scale-95 transition-all duration-200 relative touch-manipulation"
            >
              {/* Trending Badge */}
              {character.isTrending && (
                <div className="absolute top-3 left-3 z-10 bg-orange-500 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center">
                  <FireIconSolid className="h-3 w-3 mr-1" />
                  Trending
                </div>
              )}

              {/* Like Button */}
              <button className="absolute top-3 right-3 z-10 p-2 rounded-full bg-white/90 dark:bg-gray-800/90 text-gray-600 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400 transition-all duration-200 active:scale-95 touch-manipulation">
                {character.isLiked ? (
                  <HeartIconSolid className="h-4 w-4 text-red-500" />
                ) : (
                  <HeartIcon className="h-4 w-4" />
                )}
              </button>

              {/* Character Info */}
              <div className="flex items-start mb-4">
                <div className={`avatar-lg sm:avatar-xl mr-3 sm:mr-4 flex-shrink-0 bg-gradient-to-br ${character.avatar}`}>
                  {character.name.charAt(0)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white truncate pr-2">
                      {character.name}
                    </h3>
                    <div className="flex items-center flex-shrink-0">
                      <StarIconSolid className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-400" />
                      <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 ml-1">
                        {character.rating}
                      </span>
                    </div>
                  </div>
                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1 capitalize">
                    {character.category}
                  </p>
                  <div className="flex items-center text-xs text-gray-400">
                    <ChatBubbleLeftRightIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    <span>{character.chats} chats</span>
                  </div>
                </div>
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                {character.description}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  {[1,2,3,4,5].map((star) => (
                    <StarIconSolid 
                      key={star} 
                      className={`h-3 w-3 sm:h-4 sm:w-4 ${
                        star <= Math.floor(character.rating) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'
                      }`}
                    />
                  ))}
                </div>
                <button className="btn-primary text-xs sm:text-sm px-3 sm:px-4 py-2 group-hover:scale-105 active:scale-95 transition-all duration-200 touch-manipulation">
                  <ChatBubbleLeftRightIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  <span className="hidden xs:inline">Start Chat</span>
                  <span className="xs:hidden">Chat</span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-8 sm:mt-12">
          <button className="btn-outline px-6 sm:px-8 py-3 sm:py-4 active:scale-95 transition-all duration-200 touch-manipulation">
            Load More Characters
          </button>
        </div>
      </div>
    </div>
  )
}

export default CharacterBrowserPage
import { api } from './index'
import type {
  Character,
  CreateCharacterInput,
  UpdateCharacterInput,
  CharacterSearchResult,
  ApiResponse,
} from '../../types'

export const characterApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Create a new character
    createCharacter: builder.mutation<
      ApiResponse<{ character: Character }>,
      CreateCharacterInput
    >({
      query: (data) => ({
        url: '/auth/temp-create-character',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Character'],
    }),

    // Get characters with search and filtering
    getCharacters: builder.query<
      ApiResponse<CharacterSearchResult>,
      {
        search?: string
        tags?: string[]
        creatorId?: string
        isPublic?: boolean
        sortBy?: 'newest' | 'oldest' | 'rating' | 'popular'
        page?: number
        limit?: number
      }
    >({
      query: (params) => ({
        url: '/characters',
        params,
      }),
      providesTags: ['Character'],
    }),

    // Get character by <PERSON>
    getCharacter: builder.query<
      ApiResponse<{ character: Character }>,
      string
    >({
      query: (id) => `/characters/${id}`,
      providesTags: (result, error, id) => [{ type: 'Character', id }],
    }),

    // Update character
    updateCharacter: builder.mutation<
      ApiResponse<{ character: Character }>,
      { id: string; data: UpdateCharacterInput }
    >({
      query: ({ id, data }) => ({
        url: `/characters/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Character', id },
        'Character',
      ],
    }),

    // Delete character
    deleteCharacter: builder.mutation<
      ApiResponse<{}>,
      string
    >({
      query: (id) => ({
        url: `/characters/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Character'],
    }),

    // Fork character
    forkCharacter: builder.mutation<
      ApiResponse<{ character: Character }>,
      { 
        id: string
        data: {
          name?: string
          description?: string
          isPublic?: boolean
        }
      }
    >({
      query: ({ id, data }) => ({
        url: `/characters/${id}/fork`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Character'],
    }),

    // Rate character
    rateCharacter: builder.mutation<
      ApiResponse<{}>,
      { 
        id: string
        data: {
          rating: number
          review?: string
        }
      }
    >({
      query: ({ id, data }) => ({
        url: `/characters/${id}/rate`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Character', id },
      ],
    }),

    // Get character statistics
    getCharacterStats: builder.query<
      ApiResponse<{
        stats: {
          conversationCount: number
          messageCount: number
          rating: number
          ratingCount: number
        }
      }>,
      string
    >({
      query: (id) => `/characters/${id}/stats`,
    }),

    // Get character conversations (owner only)
    getCharacterConversations: builder.query<
      ApiResponse<{ conversations: any[] }>,
      { 
        id: string
        page?: number
        limit?: number
      }
    >({
      query: ({ id, ...params }) => ({
        url: `/characters/${id}/conversations`,
        params,
      }),
      providesTags: ['Conversation'],
    }),
  }),
})

export const {
  useCreateCharacterMutation,
  useGetCharactersQuery,
  useGetCharacterQuery,
  useUpdateCharacterMutation,
  useDeleteCharacterMutation,
  useForkCharacterMutation,
  useRateCharacterMutation,
  useGetCharacterStatsQuery,
  useGetCharacterConversationsQuery,
} = characterApi
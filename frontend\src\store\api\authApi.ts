import { api } from '../api'
import type { 
  ApiResponse, 
  User, 
  LoginInput, 
  RegisterInput 
} from '@/types'

export const authApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Authentication endpoints
    login: builder.mutation<ApiResponse<{ user: User; accessToken: string }>, LoginInput>({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['User'],
    }),
    
    register: builder.mutation<ApiResponse<{ user: User; accessToken: string }>, RegisterInput>({
      query: (userData) => ({
        url: '/auth/register',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['User'],
    }),
    
    logout: builder.mutation<ApiResponse, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['User'],
    }),
    
    getCurrentUser: builder.query<ApiResponse<{ user: User }>, void>({
      query: () => '/auth/me',
      providesTags: ['User'],
    }),
    
    refreshToken: builder.mutation<ApiResponse<{ user: User; accessToken: string }>, void>({
      query: () => ({
        url: '/auth/refresh',
        method: 'POST',
      }),
    }),
    
    updateProfile: builder.mutation<ApiResponse<{ user: User }>, Partial<User>>({
      query: (updates) => ({
        url: '/auth/profile',
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['User'],
    }),
    
    changePassword: builder.mutation<ApiResponse, { 
      currentPassword: string; 
      newPassword: string; 
      confirmNewPassword: string 
    }>({
      query: (passwords) => ({
        url: '/auth/password',
        method: 'PUT',
        body: passwords,
      }),
    }),
    
    getUserStats: builder.query<ApiResponse<{ 
      stats: {
        charactersCreated: number;
        conversationsStarted: number;
        messagesSent: number;
      }
    }>, void>({
      query: () => '/auth/stats',
      providesTags: ['User'],
    }),
  }),
})

export const {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useRefreshTokenMutation,
  useUpdateProfileMutation,
  useChangePasswordMutation,
  useGetUserStatsQuery,
} = authApi
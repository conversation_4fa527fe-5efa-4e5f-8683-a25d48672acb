import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { AppError } from './errorHandler';

// Generic validation middleware factory
export const validate = (schema: ZodSchema, source: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      let data;
      switch (source) {
        case 'body':
          data = req.body;
          break;
        case 'query':
          data = req.query;
          break;
        case 'params':
          data = req.params;
          break;
        default:
          data = req.body;
      }

      const validatedData = schema.parse(data);
      
      // Replace the original data with validated and transformed data
      switch (source) {
        case 'body':
          req.body = validatedData;
          break;
        case 'query':
          req.query = validatedData;
          break;
        case 'params':
          req.params = validatedData;
          break;
      }

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
        }));
        
        return next(new AppError('Validation failed', 400, errorMessages));
      }
      next(error);
    }
  };
};

// Validate request body
export const validateBody = (schema: ZodSchema) => validate(schema, 'body');

// Validate query parameters
export const validateQuery = (schema: ZodSchema) => validate(schema, 'query');

// Validate URL parameters
export const validateParams = (schema: ZodSchema) => validate(schema, 'params');

// Sanitize input to prevent XSS
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return obj
        .replace(/[<>]/g, '') // Remove < and > characters
        .trim();
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};

// Convert string numbers in query params to actual numbers
export const parseNumericQuery = (req: Request, res: Response, next: NextFunction) => {
  const parseValue = (value: any): any => {
    if (typeof value === 'string') {
      // Try to parse as number
      const numValue = Number(value);
      if (!isNaN(numValue)) {
        return numValue;
      }
      
      // Try to parse as boolean
      if (value.toLowerCase() === 'true') return true;
      if (value.toLowerCase() === 'false') return false;
    }
    
    if (Array.isArray(value)) {
      return value.map(parseValue);
    }
    
    return value;
  };

  if (req.query) {
    const parsedQuery: any = {};
    for (const [key, value] of Object.entries(req.query)) {
      parsedQuery[key] = parseValue(value);
    }
    req.query = parsedQuery;
  }

  next();
};

export default validate;
-- Run this script as the postgres superuser to set up the database
-- Example: psql -U postgres -f setup-local-db.sql

-- Create user
CREATE USER characterai WITH PASSWORD 'characterai_password';

-- Create database
CREATE DATABASE characterai_dev OWNER characterai;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE characterai_dev TO characterai;

-- Connect to the database to set up schema permissions
\c characterai_dev

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO characterai;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO characterai;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO characterai;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO characterai;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO characterai;
import React from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Link } from 'react-router-dom'
import { Menu, MenuButton, MenuItems, MenuItem, Transition } from '@headlessui/react'
import { 
  Bars3Icon, 
  BellIcon, 
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { RootState } from '@/store'
import { toggleSidebar } from '@/store/slices/uiSlice'
import { logout } from '@/store/slices/authSlice'
import { useLogoutMutation } from '@/store/api/authApi'

const Header: React.FC = () => {
  const dispatch = useDispatch()
  const { user } = useSelector((state: RootState) => state.auth)
  const [logoutMutation] = useLogoutMutation()

  const handleLogout = async () => {
    try {
      await logoutMutation().unwrap()
      dispatch(logout())
    } catch (error) {
      // Handle error if needed
      dispatch(logout()) // Logout anyway
    }
  }

  return (
    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md shadow-sm border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-30">
      <div className="px-3 sm:px-4 lg:px-6">
        <div className="flex justify-between h-14 sm:h-16">
          <div className="flex items-center min-w-0 flex-1">
            <button
              type="button"
              className="p-2 -m-2 text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 lg:hidden rounded-xl transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 active:scale-95 touch-manipulation tap-highlight-none"
              onClick={() => dispatch(toggleSidebar())}
            >
              <span className="sr-only">Open sidebar</span>
              <Bars3Icon className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>
            
            <div className="hidden lg:flex lg:items-center lg:space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-md">
                <SparklesIcon className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-lg font-bold text-gradient">Character.AI</h1>
            </div>
            
            {/* Mobile logo */}
            <div className="flex items-center lg:hidden ml-2 sm:ml-3 min-w-0">
              <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg sm:rounded-xl flex items-center justify-center shadow-md">
                <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
              <h1 className="text-base sm:text-lg font-bold text-gradient ml-2 truncate">Character.AI</h1>
            </div>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Notifications */}
            <button
              type="button"
              className="p-2 -m-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-300 rounded-xl transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 active:scale-95 relative"
            >
              <span className="sr-only">View notifications</span>
              <BellIcon className="h-5 w-5 sm:h-6 sm:w-6" />
              {/* Notification badge */}
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            </button>

            {/* Profile dropdown */}
            <div className="relative">
              <Menu>
                <div>
                  <MenuButton className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 p-0.5 hover:ring-2 hover:ring-primary-200 dark:hover:ring-primary-700 transition-all duration-200 active:scale-95">
                    <span className="sr-only">Open user menu</span>
                    {user?.avatarUrl ? (
                      <img
                        className="h-7 w-7 sm:h-8 sm:w-8 rounded-full object-cover ring-2 ring-white dark:ring-gray-700 shadow-sm"
                        src={user.avatarUrl}
                        alt={user.displayName}
                      />
                    ) : (
                      <div className="h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white text-xs sm:text-sm font-semibold ring-2 ring-white dark:ring-gray-700 shadow-sm">
                        {user?.displayName?.charAt(0)?.toUpperCase() || 'U'}
                      </div>
                    )}
                  </MenuButton>
                </div>
                
                <Transition
                  as={React.Fragment}
                  enter="transition ease-out duration-200"
                  enterFrom="transform opacity-0 scale-95 translate-y-1"
                  enterTo="transform opacity-100 scale-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="transform opacity-100 scale-100 translate-y-0"
                  leaveTo="transform opacity-0 scale-95 translate-y-1"
                >
                  <MenuItems className="absolute right-0 z-50 mt-3 w-56 sm:w-64 origin-top-right rounded-2xl bg-white dark:bg-gray-800 py-2 shadow-2xl ring-1 ring-black/5 dark:ring-white/10 focus:outline-none backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50">
                    <div className="px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                      <div className="flex items-center space-x-3">
                        {user?.avatarUrl ? (
                          <img
                            className="h-10 w-10 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600"
                            src={user.avatarUrl}
                            alt={user.displayName}
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white font-semibold ring-2 ring-gray-200 dark:ring-gray-600">
                            {user?.displayName?.charAt(0)?.toUpperCase() || 'U'}
                          </div>
                        )}
                        <div className="min-w-0 flex-1">
                          <div className="font-semibold text-gray-900 dark:text-gray-100 truncate">{user?.displayName || 'User'}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 truncate">@{user?.username || 'username'}</div>
                        </div>
                      </div>
                    </div>
                    
                    <MenuItem>
                      {({ active, close }: { active: boolean; close: () => void }) => (
                        <Link
                          to="/profile"
                          onClick={close}
                          className={`${
                            active ? 'bg-gray-50 dark:bg-gray-700' : ''
                          } flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 mx-2 rounded-xl active:scale-95`}
                        >
                          <UserCircleIcon className="mr-3 h-5 w-5 text-gray-400 dark:text-gray-500" />
                          Profile
                        </Link>
                      )}
                    </MenuItem>

                    <MenuItem>
                      {({ active, close }: { active: boolean; close: () => void }) => (
                        <Link
                          to="/settings"
                          onClick={close}
                          className={`${
                            active ? 'bg-gray-50 dark:bg-gray-700' : ''
                          } flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 mx-2 rounded-xl active:scale-95`}
                        >
                          <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-400 dark:text-gray-500" />
                          Settings
                        </Link>
                      )}
                    </MenuItem>

                    <div className="border-t border-gray-100 dark:border-gray-700 my-2"></div>

                    <MenuItem>
                      {({ active, close }: { active: boolean; close: () => void }) => (
                        <button
                          onClick={() => {
                            handleLogout()
                            close()
                          }}
                          className={`${
                            active ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400' : 'text-gray-700 dark:text-gray-300'
                          } flex w-full items-center px-4 py-3 text-sm font-medium hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-400 transition-colors duration-200 mx-2 rounded-xl active:scale-95`}
                        >
                          <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5" />
                          Sign out
                        </button>
                      )}
                    </MenuItem>
                  </MenuItems>
                </Transition>
              </Menu>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Header
{"name": "character-ai-backend", "version": "1.0.0", "description": "Backend API for Character.AI clone", "main": "dist/server.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "prisma migrate dev", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:generate": "prisma generate"}, "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "helpingai": "^1.0.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "prisma": "^5.7.1", "redis": "^4.6.11", "socket.io": "^4.7.4", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": ["ai", "chatbot", "character", "express", "typescript"], "author": "Character AI <PERSON>", "license": "MIT"}
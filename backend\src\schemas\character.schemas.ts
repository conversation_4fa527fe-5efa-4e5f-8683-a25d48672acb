import { z } from 'zod';

// Personality traits schema
const personalityTraitsSchema = z.object({
  traits: z
    .array(z.string().min(1).max(50))
    .min(1, 'At least one personality trait is required')
    .max(20, 'Maximum 20 personality traits allowed'),
  communicationStyle: z.enum(['casual', 'formal', 'playful', 'professional', 'poetic'], {
    errorMap: () => ({ message: 'Communication style must be one of: casual, formal, playful, professional, poetic' })
  }),
  background: z
    .string()
    .min(10, 'Background must be at least 10 characters long')
    .max(2000, 'Background must be less than 2000 characters')
    .trim(),
  goals: z
    .array(z.string().min(1).max(200))
    .min(1, 'At least one goal is required')
    .max(10, 'Maximum 10 goals allowed'),
  quirks: z
    .array(z.string().min(1).max(200))
    .max(10, 'Maximum 10 quirks allowed')
    .optional()
    .default([]),
});

// Create character schema
export const createCharacterSchema = z.object({
  name: z
    .string()
    .min(1, 'Character name is required')
    .max(100, 'Character name must be less than 100 characters')
    .trim(),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters long')
    .max(1000, 'Description must be less than 1000 characters')
    .trim(),
  personality: personalityTraitsSchema,
  avatarUrl: z
    .string()
    .url('Please provide a valid avatar URL')
    .optional()
    .nullable(),
  isPublic: z
    .boolean()
    .optional()
    .default(true),
  tags: z
    .array(z.string().min(1).max(30))
    .min(1, 'At least one tag is required')
    .max(10, 'Maximum 10 tags allowed')
    .refine((tags) => tags.every(tag => /^[a-zA-Z0-9\s-]+$/.test(tag)), {
      message: 'Tags can only contain letters, numbers, spaces, and hyphens'
    }),
});

// Update character schema (all fields optional)
export const updateCharacterSchema = z.object({
  name: z
    .string()
    .min(1, 'Character name is required')
    .max(100, 'Character name must be less than 100 characters')
    .trim()
    .optional(),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters long')
    .max(1000, 'Description must be less than 1000 characters')
    .trim()
    .optional(),
  personality: personalityTraitsSchema.optional(),
  avatarUrl: z
    .string()
    .url('Please provide a valid avatar URL')
    .optional()
    .nullable(),
  isPublic: z
    .boolean()
    .optional(),
  tags: z
    .array(z.string().min(1).max(30))
    .min(1, 'At least one tag is required')
    .max(10, 'Maximum 10 tags allowed')
    .refine((tags) => tags.every(tag => /^[a-zA-Z0-9\s-]+$/.test(tag)), {
      message: 'Tags can only contain letters, numbers, spaces, and hyphens'
    })
    .optional(),
});

// Character search/filter schema
export const characterFiltersSchema = z.object({
  search: z
    .string()
    .max(100, 'Search query must be less than 100 characters')
    .optional(),
  tags: z
    .array(z.string())
    .optional(),
  creatorId: z
    .string()
    .cuid('Invalid creator ID')
    .optional(),
  isPublic: z
    .boolean()
    .optional(),
  sortBy: z
    .enum(['newest', 'oldest', 'rating', 'popular'])
    .optional()
    .default('newest'),
  page: z
    .number()
    .int()
    .min(1, 'Page must be at least 1')
    .optional()
    .default(1),
  limit: z
    .number()
    .int()
    .min(1, 'Limit must be at least 1')
    .max(50, 'Limit cannot exceed 50')
    .optional()
    .default(12),
});

// Character ID parameter schema
export const characterIdSchema = z.object({
  id: z
    .string()
    .cuid('Invalid character ID'),
});

// Fork character schema
export const forkCharacterSchema = z.object({
  name: z
    .string()
    .min(1, 'Character name is required')
    .max(100, 'Character name must be less than 100 characters')
    .trim()
    .optional(),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters long')
    .max(1000, 'Description must be less than 1000 characters')
    .trim()
    .optional(),
  isPublic: z
    .boolean()
    .optional()
    .default(true),
});

// Character rating schema
export const rateCharacterSchema = z.object({
  rating: z
    .number()
    .min(1, 'Rating must be at least 1')
    .max(5, 'Rating cannot exceed 5')
    .int('Rating must be a whole number'),
  review: z
    .string()
    .max(500, 'Review must be less than 500 characters')
    .trim()
    .optional(),
});

export type CreateCharacterInput = z.infer<typeof createCharacterSchema>;
export type UpdateCharacterInput = z.infer<typeof updateCharacterSchema>;
export type CharacterFiltersInput = z.infer<typeof characterFiltersSchema>;
export type CharacterIdInput = z.infer<typeof characterIdSchema>;
export type ForkCharacterInput = z.infer<typeof forkCharacterSchema>;
export type RateCharacterInput = z.infer<typeof rateCharacterSchema>;
export interface CreateConversationInput {
  characterId: string;
  title?: string;
}

export interface SendMessageInput {
  conversationId: string;
  content: string;
}

export interface Conversation {
  id: string;
  title?: string;
  lastMessageAt: Date;
  messageCount: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  characterId: string;
  character?: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  user?: {
    id: string;
    username: string;
    displayName: string;
  };
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    [key: string]: any;
  };
  createdAt: Date;
  conversationId: string;
  userId?: string;
  user?: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
  };
}

export interface PaginationInput {
  page?: number;
  limit?: number;
  cursor?: string;
}

export interface ConversationFilters extends PaginationInput {
  characterId?: string;
  search?: string;
  sortBy?: 'newest' | 'oldest' | 'mostActive';
}

export interface MessageFilters extends PaginationInput {
  conversationId: string;
  role?: 'user' | 'assistant';
  beforeDate?: Date;
  afterDate?: Date;
}

export interface ConversationSearchResult {
  conversations: Conversation[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface MessageSearchResult {
  messages: Message[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
import { logger } from '@/utils/logger';
import { prisma } from '@/config/database';
import { redisService } from '@/config/redis';
import { getConfig } from '@/config/services';

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    ai: ServiceHealth;
  };
  environment: string;
  uptime: number;
}

export interface ServiceHealth {
  status: 'healthy' | 'unhealthy' | 'disabled';
  responseTime?: number;
  error?: string;
  details?: any;
}

export class HealthService {
  private startTime = Date.now();

  async getHealthStatus(): Promise<HealthStatus> {
    const config = getConfig();
    const timestamp = new Date().toISOString();
    const uptime = Date.now() - this.startTime;

    const [database, redis, ai] = await Promise.allSettled([
      this.checkDatabaseHealth(),
      this.checkRedisHealth(),
      this.checkAIHealth()
    ]);

    const services = {
      database: database.status === 'fulfilled' ? database.value : { status: 'unhealthy' as const, error: database.reason?.message },
      redis: redis.status === 'fulfilled' ? redis.value : { status: 'unhealthy' as const, error: redis.reason?.message },
      ai: ai.status === 'fulfilled' ? ai.value : { status: 'unhealthy' as const, error: ai.reason?.message }
    };

    // Determine overall status
    const serviceStatuses = Object.values(services).map(s => s.status);
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded';

    if (serviceStatuses.every(s => s === 'healthy' || s === 'disabled')) {
      overallStatus = 'healthy';
    } else if (serviceStatuses.some(s => s === 'healthy')) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'unhealthy';
    }

    return {
      status: overallStatus,
      timestamp,
      services,
      environment: config.environment,
      uptime
    };
  }

  private async checkDatabaseHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Simple database connectivity check
      await prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;
      
      logger.debug(`Database health check: healthy (${responseTime}ms)`);
      return {
        status: 'healthy',
        responseTime
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('Database health check failed:', error);
      
      return {
        status: 'unhealthy',
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown database error'
      };
    }
  }

  private async checkRedisHealth(): Promise<ServiceHealth> {
    const config = getConfig();
    
    if (!config.redis.enabled) {
      return {
        status: 'disabled',
        details: 'Redis is disabled in current configuration'
      };
    }

    const startTime = Date.now();
    
    try {
      const isHealthy = await redisService.healthCheck();
      const responseTime = Date.now() - startTime;
      
      if (isHealthy) {
        logger.debug(`Redis health check: healthy (${responseTime}ms)`);
        return {
          status: 'healthy',
          responseTime
        };
      } else {
        logger.warn(`Redis health check: unhealthy (${responseTime}ms)`);
        return {
          status: 'unhealthy',
          responseTime,
          error: 'Redis ping failed'
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('Redis health check failed:', error);
      
      return {
        status: 'unhealthy',
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown Redis error'
      };
    }
  }

  private async checkAIHealth(): Promise<ServiceHealth> {
    const config = getConfig();
    const startTime = Date.now();
    
    try {
      if (config.ai.provider === 'mock') {
        return {
          status: 'disabled',
          details: 'AI service is running in mock mode'
        };
      }

      // For now, we'll just check if the API key is configured
      // Later this will be replaced with actual AI service health check
      if (!config.ai.apiKey) {
        return {
          status: 'unhealthy',
          error: 'AI service API key not configured'
        };
      }

      const responseTime = Date.now() - startTime;
      
      // Mock health check for now - will be replaced with real implementation
      logger.debug(`AI service health check: healthy (${responseTime}ms) - mock check`);
      return {
        status: 'healthy',
        responseTime,
        details: 'Mock AI health check - will be replaced with real implementation'
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('AI service health check failed:', error);
      
      return {
        status: 'unhealthy',
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown AI service error'
      };
    }
  }

  // Quick health check for load balancers
  async isHealthy(): Promise<boolean> {
    try {
      const health = await this.getHealthStatus();
      return health.status === 'healthy' || health.status === 'degraded';
    } catch (error) {
      logger.error('Health check failed:', error);
      return false;
    }
  }
}

export const healthService = new HealthService();
export default healthService;

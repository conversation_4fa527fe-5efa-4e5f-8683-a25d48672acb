import winston from 'winston';

const { combine, timestamp, errors, json, simple, colorize } = winston.format;

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(colors);

// Create the logger
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels,
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
    errors({ stack: true }),
    json()
  ),
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: process.env.NODE_ENV === 'development' 
        ? combine(
            colorize({ all: true }),
            simple()
          )
        : json(),
    }),
    
    // File transport for production
    ...(process.env.NODE_ENV === 'production' 
      ? [
          new winston.transports.File({
            filename: 'logs/error.log',
            level: 'error',
          }),
          new winston.transports.File({
            filename: 'logs/combined.log',
          }),
        ]
      : []
    ),
  ],
});

// Create a stream object for Morgan
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

export default logger;
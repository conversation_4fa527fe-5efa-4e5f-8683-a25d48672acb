import { PrismaClient } from '@prisma/client';
import { ConversationService } from './conversation.service';
import { AIService, GenerateResponseInput } from './ai.service';
import { RedisService } from '@/config/redis';
import { logger } from '@/utils/logger';
import { AppError } from '@/middleware/errorHandler';
import { Message } from '@/types/conversation';

export class ChatService {
  private conversationService: ConversationService;
  private aiService: AIService;

  constructor(
    private prisma: PrismaClient,
    private redisService: RedisService
  ) {
    this.conversationService = new ConversationService(prisma);
    this.aiService = new AIService();
  }

  // Process a user message and generate AI response
  async processMessage(
    conversationId: string,
    content: string,
    userId: string
  ): Promise<{ userMessage: Message; aiResponse: Message }> {
    const startTime = Date.now();

    try {
      // Verify conversation access
      const conversation = await this.conversationService.getConversation(conversationId, userId);

      // Get character information
      const character = await this.prisma.character.findUnique({
        where: { id: conversation.characterId },
      });

      if (!character) {
        throw new AppError('Character not found', 404);
      }

      // Get user profile
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          username: true,
          displayName: true,
        },
      });

      // Create user message
      const userMessage = await this.prisma.message.create({
        data: {
          conversationId,
          content,
          role: 'user',
          userId,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      });

      // Get conversation history for context
      const conversationHistory = await this.prisma.message.findMany({
        where: { conversationId },
        orderBy: { createdAt: 'asc' },
        take: 20, // Last 20 messages for context
        select: {
          role: true,
          content: true,
          createdAt: true,
        },
      });

      // Prepare AI input
      const aiInput: GenerateResponseInput = {
        character: {
          id: character.id,
          name: character.name,
          personality: character.personality as any,
        },
        conversation: conversationHistory as { role: 'user' | 'assistant'; content: string; createdAt: Date; }[],
        userMessage: content,
        userProfile: user || undefined,
      };

      // Generate AI response
      const aiResponseContent = await this.aiService.generateResponse(aiInput);
      const processingTime = Date.now() - startTime;

      // Create AI response message
      const aiResponse = await this.prisma.message.create({
        data: {
          conversationId,
          content: aiResponseContent,
          role: 'assistant',
          metadata: JSON.stringify({
            model: process.env.HELPINGAI_MODEL || 'Dhanishtha-2.0-preview',
            processingTime,
            tokens: this.aiService.estimateTokens(aiResponseContent),
            characterId: character.id,
          }),
        },
      });

      // Update conversation
      await this.prisma.conversation.update({
        where: { id: conversationId },
        data: {
          lastMessageAt: new Date(),
          messageCount: { increment: 2 }, // User message + AI response
        },
      });

      // Update character conversation count if this is a new conversation
      if (conversationHistory.length === 0) {
        await this.prisma.character.update({
          where: { id: character.id },
          data: {
            conversationCount: { increment: 1 },
          },
        });
      }

      logger.info(`Chat message processed: conversation ${conversationId}, processing time: ${processingTime}ms`);

      return { 
        userMessage: userMessage as any, 
        aiResponse: aiResponse as any 
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Process message error:', error);
      throw new AppError('Failed to process message', 500);
    }
  }

  // Get typing status for a conversation
  async getTypingUsers(conversationId: string): Promise<string[]> {
    try {
      // Get typing users from Redis
      const typingKey = `typing:${conversationId}`;
      const typingUsers = await this.redisService.getMembers(typingKey);
      return typingUsers || [];
    } catch (error) {
      logger.error('Get typing users error:', error);
      return [];
    }
  }

  // Set typing status
  async setTypingStatus(conversationId: string, userId: string, isTyping: boolean): Promise<void> {
    try {
      const typingKey = `typing:${conversationId}`;
      
      if (isTyping) {
        // Add user to typing set with expiration
        await this.redisService.setAdd(typingKey, userId);
        await this.redisService.expire(typingKey, 10); // Expire in 10 seconds
      } else {
        // Remove user from typing set
        await this.redisService.setRemove(typingKey, userId);
      }
      
      logger.debug(`User ${userId} ${isTyping ? 'started' : 'stopped'} typing in conversation ${conversationId}`);
    } catch (error) {
      logger.error('Set typing status error:', error);
    }
  }

  // Get conversation context for AI
  private async getConversationContext(conversationId: string, limit: number = 10): Promise<any[]> {
    return this.prisma.message.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        role: true,
        content: true,
        createdAt: true,
        metadata: true,
      },
    });
  }

  // Regenerate AI response
  async regenerateResponse(messageId: string, userId: string): Promise<Message> {
    try {
      // Get the message to regenerate
      const originalMessage = await this.prisma.message.findUnique({
        where: { id: messageId },
        include: {
          conversation: {
            include: {
              character: true,
            },
          },
        },
      });

      if (!originalMessage) {
        throw new AppError('Message not found', 404);
      }

      if (originalMessage.role !== 'assistant') {
        throw new AppError('Can only regenerate AI responses', 400);
      }

      if (originalMessage.conversation.userId !== userId) {
        throw new AppError('Not authorized to regenerate this response', 403);
      }

      // Get the user message that prompted this response
      const userMessage = await this.prisma.message.findFirst({
        where: {
          conversationId: originalMessage.conversationId,
          createdAt: { lt: originalMessage.createdAt },
          role: 'user',
        },
        orderBy: { createdAt: 'desc' },
      });

      if (!userMessage) {
        throw new AppError('Original user message not found', 404);
      }

      // Get conversation history (excluding the original AI response)
      const conversationHistory = await this.prisma.message.findMany({
        where: {
          conversationId: originalMessage.conversationId,
          createdAt: { lt: originalMessage.createdAt },
        },
        orderBy: { createdAt: 'asc' },
        take: 20,
        select: {
          role: true,
          content: true,
          createdAt: true,
        },
      });

      // Get user profile
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          username: true,
          displayName: true,
        },
      });

      // Prepare AI input
      const aiInput: GenerateResponseInput = {
        character: {
          id: originalMessage.conversation.character.id,
          name: originalMessage.conversation.character.name,
          personality: originalMessage.conversation.character.personality as any,
        },
        conversation: conversationHistory as { role: 'user' | 'assistant'; content: string; createdAt: Date; }[],
        userMessage: userMessage.content,
        userProfile: user || undefined,
      };

      // Generate new AI response
      const newResponseContent = await this.aiService.generateResponse(aiInput);

      // Update the original message
      const updatedMessage = await this.prisma.message.update({
        where: { id: messageId },
        data: {
          content: newResponseContent,
          metadata: JSON.stringify({
            ...((originalMessage.metadata ? JSON.parse(originalMessage.metadata as string) : {}) || {}),
            regenerated: true,
            regeneratedAt: new Date(),
            model: process.env.HELPINGAI_MODEL || 'Dhanishtha-2.0-preview',
            tokens: this.aiService.estimateTokens(newResponseContent),
          }),
        },
      });

      logger.info(`AI response regenerated: message ${messageId}`);
      return updatedMessage as any;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Regenerate response error:', error);
      throw new AppError('Failed to regenerate response', 500);
    }
  }

  // Delete a message
  async deleteMessage(messageId: string, userId: string): Promise<void> {
    try {
      const message = await this.prisma.message.findUnique({
        where: { id: messageId },
        include: {
          conversation: true,
        },
      });

      if (!message) {
        throw new AppError('Message not found', 404);
      }

      // Check if user owns the conversation or the message
      if (message.conversation.userId !== userId && message.userId !== userId) {
        throw new AppError('Not authorized to delete this message', 403);
      }

      // Delete the message
      await this.prisma.message.delete({
        where: { id: messageId },
      });

      // Update conversation message count
      await this.prisma.conversation.update({
        where: { id: message.conversationId },
        data: {
          messageCount: { decrement: 1 },
        },
      });

      logger.info(`Message deleted: ${messageId} by user ${userId}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Delete message error:', error);
      throw new AppError('Failed to delete message', 500);
    }
  }

  // Get AI service health status
  async getAIHealthStatus(): Promise<{ available: boolean; models: string[] }> {
    try {
      // Mock health check
      return { available: true, models: ['mock-model-1', 'mock-model-2'] };
    } catch (error) {
      logger.error('AI health check error:', error);
      return { available: false, models: [] };
    }
  }
}

export default ChatService;
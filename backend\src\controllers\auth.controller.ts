import { Request, Response } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { AuthService } from '@/services/auth.service';
import { prisma } from '@/config/database';
import { redisService } from '@/config/redis';
import {
  RegisterInput,
  LoginInput,
} from '@/types/auth';

// Initialize auth service
const authService = new AuthService(prisma, redisService);

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
export const register = asyncHandler(async (req: Request, res: Response) => {
  const userData: RegisterInput = req.body;
  
  logger.info('Registration attempt:', { email: userData.email, username: userData.username });

  const result = await authService.register(userData);

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: result,
  });
});

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
export const login = asyncHandler(async (req: Request, res: Response) => {
  const loginData: LoginInput = req.body;
  
  logger.info('Login attempt:', { email: loginData.email });

  const result = await authService.login(loginData);

  res.status(200).json({
    success: true,
    message: 'Login successful',
    data: result,
  });
});

// @desc    Get current user info
// @route   GET /api/auth/me
// @access  Private
export const getCurrentUser = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  
  const user = await authService.getCurrentUser(userId);

  res.status(200).json({
    success: true,
    data: { user },
  });
});

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Private
export const logout = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const refreshToken = req.body.refreshToken;
  
  await authService.logout(userId, refreshToken);
  
  logger.info('Logout successful');
  
  res.status(200).json({
    success: true,
    message: 'Logout successful',
  });
});

export default { register, login, getCurrentUser, logout };
import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';
import { AppError } from '@/middleware/errorHandler';
import {
  CreateCharacterInput,
  UpdateCharacterInput,
  CharacterFilters,
  CharacterSearchResult,
  Character,
  PersonalityTraits,
} from '@/types/character';

export class CharacterService {
  constructor(private prisma: PrismaClient) {}

  // Helper function to serialize personality for SQLite
  private serializePersonality(personality: PersonalityTraits): string {
    return JSON.stringify(personality);
  }

  // Helper function to deserialize personality from SQLite
  private deserializePersonality(personality: string): PersonalityTraits {
    try {
      return JSON.parse(personality);
    } catch (error) {
      logger.error('Failed to parse personality JSON:', error);
      return {} as PersonalityTraits;
    }
  }

  // Helper function to serialize tags for SQLite
  private serializeTags(tags: string[]): string {
    return tags.join(',');
  }

  // Helper function to deserialize tags from SQLite
  private deserializeTags(tags: string): string[] {
    return tags ? tags.split(',').filter(tag => tag.trim() !== '') : [];
  }

  // Create a new character
  async create<PERSON><PERSON>cter(data: CreateCharacterInput, userId: string): Promise<Character> {
    try {
      const character = await this.prisma.character.create({
        data: {
          ...data,
          personality: this.serializePersonality(data.personality),
          tags: this.serializeTags(data.tags),
          creatorId: userId,
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      });

      logger.info(`Character created: ${character.name} by user ${userId}`);
      return {
        ...character,
        personality: this.deserializePersonality(character.personality),
        tags: this.deserializeTags(character.tags),
      } as Character;
    } catch (error) {
      logger.error('Create character error:', error);
      throw new AppError('Failed to create character', 500);
    }
  }

  // Get character by ID
  async getCharacter(id: string, userId?: string): Promise<Character> {
    try {
      const character = await this.prisma.character.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      });

      if (!character) {
        throw new AppError('Character not found', 404);
      }

      // Check if user has access to private character
      if (!character.isPublic && character.creatorId !== userId) {
        throw new AppError('Character not found', 404);
      }

      return {
        ...character,
        personality: this.deserializePersonality(character.personality),
        tags: this.deserializeTags(character.tags),
      } as Character;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Get character error:', error);
      throw new AppError('Failed to get character', 500);
    }
  }

  // Update character (owner only)
  async updateCharacter(id: string, data: UpdateCharacterInput, userId: string): Promise<Character> {
    try {
      // Check if character exists and user owns it
      const existingCharacter = await this.prisma.character.findUnique({
        where: { id },
      });

      if (!existingCharacter) {
        throw new AppError('Character not found', 404);
      }

      if (existingCharacter.creatorId !== userId) {
        throw new AppError('Not authorized to update this character', 403);
      }

      const character = await this.prisma.character.update({
        where: { id },
        data: {
          ...data,
          personality: data.personality ? this.serializePersonality(data.personality) : undefined,
          tags: data.tags ? this.serializeTags(data.tags) : undefined,
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      });

      logger.info(`Character updated: ${character.name} by user ${userId}`);
      return {
        ...character,
        personality: this.deserializePersonality(character.personality),
        tags: this.deserializeTags(character.tags),
      } as Character;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Update character error:', error);
      throw new AppError('Failed to update character', 500);
    }
  }

  // Delete character (owner only)
  async deleteCharacter(id: string, userId: string): Promise<void> {
    try {
      // Check if character exists and user owns it
      const existingCharacter = await this.prisma.character.findUnique({
        where: { id },
      });

      if (!existingCharacter) {
        throw new AppError('Character not found', 404);
      }

      if (existingCharacter.creatorId !== userId) {
        throw new AppError('Not authorized to delete this character', 403);
      }

      // Delete character (conversations and messages will be cascade deleted)
      await this.prisma.character.delete({
        where: { id },
      });

      logger.info(`Character deleted: ${existingCharacter.name} by user ${userId}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Delete character error:', error);
      throw new AppError('Failed to delete character', 500);
    }
  }

  // Search characters with filters
  async searchCharacters(filters: CharacterFilters, userId?: string): Promise<CharacterSearchResult> {
    try {
      const {
        search,
        tags,
        creatorId,
        isPublic = true,
        sortBy = 'newest',
        page = 1,
        limit = 12,
      } = filters;

      const offset = (page - 1) * limit;

      // Build where clause
      const where: any = {
        // Only show public characters unless user is viewing their own
        AND: [
          isPublic !== undefined ? { isPublic } : {},
          creatorId ? { creatorId } : {},
          search ? {
            OR: [
              { name: { contains: search, mode: 'insensitive' } },
              { description: { contains: search, mode: 'insensitive' } },
            ],
          } : {},
          tags && tags.length > 0 ? {
            OR: tags.map(tag => ({
              tags: { contains: tag }
            }))
          } : {},
        ].filter(condition => Object.keys(condition).length > 0),
      };

      // If user is authenticated and not filtering by public, show their private characters too
      if (userId && isPublic === undefined) {
        where.OR = [
          { isPublic: true },
          { creatorId: userId },
        ];
        delete where.AND;
      }

      // Build order by clause
      let orderBy: any = {};
      switch (sortBy) {
        case 'oldest':
          orderBy = { createdAt: 'asc' };
          break;
        case 'rating':
          orderBy = { rating: 'desc' };
          break;
        case 'popular':
          orderBy = { conversationCount: 'desc' };
          break;
        case 'newest':
        default:
          orderBy = { createdAt: 'desc' };
          break;
      }

      // Execute queries
      const [characters, total] = await Promise.all([
        this.prisma.character.findMany({
          where,
          orderBy,
          skip: offset,
          take: limit,
          include: {
            creator: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatarUrl: true,
              },
            },
          },
        }),
        this.prisma.character.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        characters: characters.map(char => ({
          ...char,
          personality: this.deserializePersonality(char.personality),
          tags: this.deserializeTags(char.tags),
        })) as Character[],
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error('Search characters error:', error);
      throw new AppError('Failed to search characters', 500);
    }
  }

  // Fork (copy) a character
  async forkCharacter(id: string, userId: string, customData?: Partial<CreateCharacterInput>): Promise<Character> {
    try {
      // Get original character
      const originalCharacter = await this.getCharacter(id, userId);

      // Create forked character
      const forkedCharacter = await this.prisma.character.create({
        data: {
          name: customData?.name || `${originalCharacter.name} (Fork)`,
          description: customData?.description || originalCharacter.description,
          personality: this.serializePersonality(originalCharacter.personality),
          avatarUrl: originalCharacter.avatarUrl,
          tags: this.serializeTags(customData?.tags || originalCharacter.tags),
          isPublic: customData?.isPublic ?? true,
          creatorId: userId,
          forkedFromId: originalCharacter.id,
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatarUrl: true,
            },
          },
          forkedFrom: {
            select: {
              id: true,
              name: true,
              creator: {
                select: {
                  username: true,
                  displayName: true,
                },
              },
            },
          },
        },
      });

      logger.info(`Character forked: ${originalCharacter.name} -> ${forkedCharacter.name} by user ${userId}`);
      return {
        ...forkedCharacter,
        personality: this.deserializePersonality(forkedCharacter.personality),
        tags: this.deserializeTags(forkedCharacter.tags),
      } as Character;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Fork character error:', error);
      throw new AppError('Failed to fork character', 500);
    }
  }

  // Rate a character
  async rateCharacter(id: string, userId: string, rating: number, review?: string): Promise<void> {
    try {
      const character = await this.getCharacter(id, userId);

      // User cannot rate their own character
      if (character.creatorId === userId) {
        throw new AppError('Cannot rate your own character', 400);
      }

      // Check if user has already rated this character
      // Note: We would need a separate Rating model for this, for now we'll skip duplicate check

      // Update character rating (simplified - in production, use separate ratings table)
      const newRatingCount = character.ratingCount + 1;
      const newRating = ((character.rating * character.ratingCount) + rating) / newRatingCount;

      await this.prisma.character.update({
        where: { id },
        data: {
          rating: newRating,
          ratingCount: newRatingCount,
        },
      });

      logger.info(`Character rated: ${character.name} rated ${rating} by user ${userId}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Rate character error:', error);
      throw new AppError('Failed to rate character', 500);
    }
  }

  // Get user's characters
  async getUserCharacters(userId: string, page: number = 1, limit: number = 12): Promise<CharacterSearchResult> {
    try {
      const offset = (page - 1) * limit;

      const [characters, total] = await Promise.all([
        this.prisma.character.findMany({
          where: { creatorId: userId },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
          include: {
            creator: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatarUrl: true,
              },
            },
          },
        }),
        this.prisma.character.count({ where: { creatorId: userId } }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        characters: characters.map(char => ({
          ...char,
          personality: this.deserializePersonality(char.personality),
          tags: this.deserializeTags(char.tags),
        })) as Character[],
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error('Get user characters error:', error);
      throw new AppError('Failed to get user characters', 500);
    }
  }

  // Get character statistics
  async getCharacterStats(id: string, userId?: string): Promise<{
    conversationCount: number;
    messageCount: number;
    rating: number;
    ratingCount: number;
    forkCount: number;
  }> {
    try {
      const character = await this.getCharacter(id, userId);

      const [messageCount, forkCount] = await Promise.all([
        this.prisma.message.count({
          where: {
            conversation: {
              characterId: id,
            },
            role: 'character',
          },
        }),
        this.prisma.character.count({
          where: { forkedFromId: id },
        }),
      ]);

      return {
        conversationCount: character.conversationCount,
        messageCount,
        rating: character.rating,
        ratingCount: character.ratingCount,
        forkCount,
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Get character stats error:', error);
      throw new AppError('Failed to get character statistics', 500);
    }
  }

  // Get trending characters
  async getTrendingCharacters(limit: number = 10): Promise<Character[]> {
    try {
      // Get characters with high conversation activity in the last 7 days
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const characters = await this.prisma.character.findMany({
        where: {
          isPublic: true,
          conversations: {
            some: {
              createdAt: {
                gte: weekAgo,
              },
            },
          },
        },
        orderBy: [
          { conversationCount: 'desc' },
          { rating: 'desc' },
        ],
        take: limit,
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      });

      return characters.map(char => ({
        ...char,
        personality: this.deserializePersonality(char.personality) as any,
        tags: this.deserializeTags(char.tags),
      })) as Character[];
    } catch (error) {
      logger.error('Get trending characters error:', error);
      throw new AppError('Failed to get trending characters', 500);
    }
  }
}

export default CharacterService;
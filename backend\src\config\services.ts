import { logger } from '@/utils/logger';

// Service configuration interfaces
export interface RedisConfig {
  enabled: boolean;
  url: string;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  connectTimeout: number;
  lazyConnect: boolean;
}

export interface AIConfig {
  provider: 'helpingai' | 'mock';
  apiKey: string;
  model: string;
  maxTokens: number;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface ModerationConfig {
  enabled: boolean;
  provider: 'helpingai' | 'keyword';
  strictMode: boolean;
  timeout: number;
}

export interface ServiceConfig {
  environment: 'development' | 'production' | 'test';
  mockServices: boolean;
  redis: RedisConfig;
  ai: AIConfig;
  moderation: ModerationConfig;
}

// Environment variable validation
export function validateEnvironment(): void {
  const required = [
    'NODE_ENV',
    'DATABASE_URL',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate production-specific requirements
  if (process.env.NODE_ENV === 'production') {
    const productionRequired = [
      'REDIS_URL',
      'HELPINGAI_API_KEY'
    ];

    const missingProduction = productionRequired.filter(key => !process.env[key]);
    
    if (missingProduction.length > 0) {
      throw new Error(`Missing required production environment variables: ${missingProduction.join(', ')}`);
    }
  }
}

// Get service configuration based on environment
export function getServiceConfig(): ServiceConfig {
  const environment = (process.env.NODE_ENV || 'development') as 'development' | 'production' | 'test';
  const mockServices = process.env.MOCK_SERVICES === 'true' || environment === 'development';

  // Redis configuration
  const redisConfig: RedisConfig = {
    enabled: !mockServices && !!process.env.REDIS_URL,
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100'),
    maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
    connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || '10000'),
    lazyConnect: true
  };

  // AI service configuration
  const aiConfig: AIConfig = {
    provider: mockServices ? 'mock' : 'helpingai',
    apiKey: process.env.HELPINGAI_API_KEY || '',
    model: process.env.HELPINGAI_MODEL || 'Dhanishtha-2.0-preview',
    maxTokens: parseInt(process.env.HELPINGAI_MAX_TOKENS || '2000'),
    timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
    retryAttempts: parseInt(process.env.AI_RETRY_ATTEMPTS || '3'),
    retryDelay: parseInt(process.env.AI_RETRY_DELAY || '1000')
  };

  // Content moderation configuration
  const moderationConfig: ModerationConfig = {
    enabled: environment === 'production' || process.env.MODERATION_ENABLED === 'true',
    provider: mockServices ? 'keyword' : 'helpingai',
    strictMode: environment === 'production',
    timeout: parseInt(process.env.MODERATION_TIMEOUT || '5000')
  };

  const config: ServiceConfig = {
    environment,
    mockServices,
    redis: redisConfig,
    ai: aiConfig,
    moderation: moderationConfig
  };

  logger.info('Service configuration loaded:', {
    environment,
    mockServices,
    redisEnabled: redisConfig.enabled,
    aiProvider: aiConfig.provider,
    moderationEnabled: moderationConfig.enabled
  });

  return config;
}

// Singleton service configuration
let serviceConfig: ServiceConfig | null = null;

export function getConfig(): ServiceConfig {
  if (!serviceConfig) {
    validateEnvironment();
    serviceConfig = getServiceConfig();
  }
  return serviceConfig;
}

export default {
  validateEnvironment,
  getServiceConfig,
  getConfig
};
